import { ExchangeProceedInterface, OrderProductInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { Form, InputNumber } from "antd";
import _ from "lodash";
import { useCallback, useMemo } from "react";
import { CSS_CLASSES } from "../constants.ts";
import { ExchangeItem, UseExchangeItemsReturn } from "../types.ts";

/**
 * Custom hook for managing exchange items logic
 */
export const useExchangeItems = (
  featureSchemes: any[],
  selectedOrderProducts: OrderProductInterface[]
): UseExchangeItemsReturn => {

  // Render exchange proceeds as JSX elements
  const renderedExchangeProceeds = useCallback(
    (exchangeProceeds: ExchangeProceedInterface[]) => {
      return exchangeProceeds.map((exchangeProceed, index) => {
        const { projectItem, projectProduct, quantity } = exchangeProceed;
        const itemName =
          projectItem?.item?.name ?? projectProduct?.product?.name;
        const unitName =
          projectProduct?.productPackaging?.unit?.name ??
          projectItem?.item.unit?.name;
        const code = projectProduct?.product?.code ?? projectItem?.item?.code;

        return (
          <p
            key={`${index}-${exchangeProceed.id}-${projectProduct?.id}-${projectItem?.id}`}
            className={CSS_CLASSES.PRODUCT_ITEM}
          >
            x{quantity} {unitName} - {code} - {itemName}
          </p>
        );
      });
    },
    [],
  );

  // Calculate selected exchange items based on feature schemes and selected products
  const selectedExchangeItems = useMemo(
    () =>
      featureSchemes.flatMap((featureScheme) => {
        const { featureSchemeExchanges } = featureScheme;

        return featureSchemeExchanges
          .filter((item: any) => !item.luckyDrawId)
          .flatMap((featureSchemeExchange: any) => {
            const { logical, exchangeConditions, id, exchangeProceeds, name } =
              featureSchemeExchange;

            // Handle exchanges with no conditions
            if (exchangeConditions.length === 0) {
              return {
                item: (
                  <Form.Item
                    name={id}
                    label={
                      <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                    }
                  >
                    <InputNumber className={CSS_CLASSES.INPUT_FULL_WIDTH} controls={false} />
                  </Form.Item>
                ),
                schemeName: name,
                id: id,
              };
            }

            // Handle "OR" logical exchanges
            if (logical === "or") {
              const hasProjectProduct = exchangeConditions.some(
                (exchangeCondition: any) =>
                  selectedOrderProducts.some(
                    (orderProduct) =>
                      orderProduct.projectProductId ===
                      exchangeCondition.projectProductId,
                  ),
              );

              if (hasProjectProduct) {
                return {
                  item: (
                    <Form.Item
                      name={id}
                      label={
                        <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                      }
                    >
                      <InputNumber className={CSS_CLASSES.INPUT_FULL_WIDTH} controls={false} />
                    </Form.Item>
                  ),
                  schemeName: name,
                  id: id,
                };
              }
            }

            // Handle "AND" logical exchanges
            if (logical === "and") {
              const selectedOrderProductsProjectProductId =
                selectedOrderProducts
                  .map((item) => item.projectProductId)
                  .sort((a, b) => a - b);
              const exchangeConditionsProjectProductId = exchangeConditions
                .map((item: any) => item.projectProductId)
                .sort((a, b) => a - b);

              if (
                _.intersection(
                  selectedOrderProductsProjectProductId,
                  exchangeConditionsProjectProductId,
                ).length === exchangeConditionsProjectProductId.length
              ) {
                return {
                  item: (
                    <Form.Item
                      name={id}
                      label={
                        <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                      }
                    >
                      <InputNumber className={CSS_CLASSES.INPUT_FULL_WIDTH} controls={false} />
                    </Form.Item>
                  ),
                  schemeName: name,
                  id: id,
                };
              }
            }

            return undefined;
          });
      }) as (ExchangeItem | undefined)[],
    [featureSchemes, renderedExchangeProceeds, selectedOrderProducts],
  );

  return {
    selectedExchangeItems,
    renderedExchangeProceeds,
  };
};
