import { formatCell<PERSON>eader, numToCol } from "@/common/export-excel.helper.ts";
import {
  ExchangeConditionInterface,
  ExchangeProceedInterface,
  FeatureCustomerDataTypeEnum,
  FeatureCustomerInterface,
} from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { ProjectInterface } from "@project/interface.ts";
import { ProjectItemInterface } from "@project/item/interface.ts";
import { ProjectProductInterface } from "@project/product/interface.ts";
import dayjs from "dayjs";
import Excel from "exceljs";
import _ from "lodash";
import {
  RecordOrderInterface,
  RecordOrderPurchaseInterface,
} from "./interface.ts";

type ProcessOrderDataPositionType = (
  | "projectId"
  | "projectName"
  | "projectOutletCode"
  | "projectOutletName"
  | "projectBoothName"
  | "timeIn"
  | "timeOut"
  | "provinceName"
  | "districtName"
  | "channelName"
  | "subChannelName"
  | "agencyName"
  | "roleName"
  | "employeeUserId"
  | "employeeUserName"
  | "leaderId"
  | "leaderName"
  | "entityId"
  | "entityCreatedAt"
  | "recordOrderCustomers"
  | "featureCustomers"
  | "otp"
)[];

export const processOrderData = (
  project: ProjectInterface | undefined,
  entity: RecordOrderInterface,
  featureCustomers: FeatureCustomerInterface[],
  position?: ProcessOrderDataPositionType,
) => {
  const { projectOutlet, projectBooth, projectAgency, leader } =
    entity.projectRecordFeature.projectRecord;
  const { projectRecordEmployee } = entity.projectRecordFeature;
  const { in: attendanceIn, out: attendanceOut } =
    entity.projectRecordFeature.attendance;
  const { recordOrderCustomers } = entity;

  position ??= [
    // "projectId",
    // "projectName",
    "projectOutletCode",
    "projectOutletName",
    // "projectBoothName",
    "timeIn",
    "timeOut",
    "provinceName",
    "districtName",
    // "channelName",
    "subChannelName",
    // "agencyName",
    // "roleName",
    "employeeUserId",
    "employeeUserName",
    "leaderId",
    "leaderName",
    "entityId",
    "entityCreatedAt",
    "recordOrderCustomers",
    "featureCustomers",
    "otp",
  ];

  return position.flatMap((item) => {
    switch (item) {
      case "projectId":
        return project?.id;
      case "projectName":
        return project?.name;
      case "projectOutletCode":
        return projectOutlet.code;
      case "projectOutletName":
        return projectOutlet.name;
      case "projectBoothName":
        return projectBooth.name;
      case "timeIn":
        return dayjs(attendanceIn.deviceTime).add(7, "hour").toDate();
      case "timeOut":
        return attendanceOut?.deviceTime
          ? dayjs(attendanceOut?.deviceTime).add(7, "hour").toDate()
          : "";
      case "provinceName":
        return projectOutlet.province?.name;
      case "districtName":
        return projectOutlet.district?.name;
      case "channelName":
        return projectOutlet.projectAgencyChannel.channel.name;
      case "subChannelName":
        return projectOutlet.subChannel?.name;
      case "agencyName":
        return projectAgency.agency.name;
      case "roleName":
        return projectRecordEmployee.employee.role.name;
      case "employeeUserId":
        return projectRecordEmployee.employee.user.id;
      case "employeeUserName":
        return projectRecordEmployee.employee.user.name;
      case "leaderId":
        return leader.id;
      case "leaderName":
        return leader.user.name;
      case "entityId":
        return entity.id;
      case "entityCreatedAt":
        return dayjs(entity.dataTimestamp).add(7, "hour").toDate();
      case "recordOrderCustomers":
        return recordOrderCustomers && recordOrderCustomers.length > 0
          ? recordOrderCustomers[0]?.recordCustomer?.projectCustomerId
          : " ";
      case "featureCustomers":
        return featureCustomers.map((featureCustomer) => {
          const recordOrderCustomer = recordOrderCustomers?.find(
            (recordOrderCustomer) =>
              recordOrderCustomer.recordCustomer.featureCustomer?.code ===
              featureCustomer.code,
          );
          const { recordCustomer } = recordOrderCustomer ?? {};

          if (
            recordCustomer?.featureCustomer?.dataType ===
            FeatureCustomerDataTypeEnum.RADIO
          ) {
            return recordCustomer?.recordCustomerOptions
              .map((item) => item.featureCustomerOption.name)
              .join(", ");
          } else {
            return recordCustomer?.value;
          }
        });
      case "otp": {
        const recordOrderCustomer = recordOrderCustomers?.find(
          (recordOrderCustomer) =>
            recordOrderCustomer.recordCustomer.otpDelivery,
        );
        return recordOrderCustomer?.recordCustomer?.otpDelivery?.otpCode;
      }
      default:
        return "";
    }
  });
};

export const getFixedHeaders = (
  featureCustomers: FeatureCustomerInterface[],
) => {
  return [
    // "ID dự án",
    // "Tên dự án",
    "Mã outlet",
    "Tên outlet",
    // "Loại booth",
    "Thời gian chấm công vào",
    "Thời gian chấm công ra",
    "Tỉnh/ TP",
    "Phường", // "Quận/ Huyện",
    // "Kênh",
    "Mức outlet",
    // "Agency phụ trách",
    // "Role nhân viên ghi nhận",
    "ID nhân viên ghi nhận",
    "Họ tên nhân viên ghi nhận",
    "ID trưởng nhóm quản lý",
    "Họ tên trưởng nhóm quản lý",
    "ID đơn hàng",
    "Thời gian ghi nhận",
    "ID khách",
    ...featureCustomers.map((item) => item.name),
    "OTP",
  ];
};

const STATISTIC_PURCHASE = "LƯỢT MUA";
const STATISTIC_REVENUE = "DOANH SỐ";
const OTHER_STATISTICS_COLUMNS = [STATISTIC_PURCHASE, STATISTIC_REVENUE];
const NO_SCHEME_EXCHANGE_NAME = "Không đổi quà";

type FeatureSchemeExchangeTotalType = {
  name: string;
  exchangeConditions: { projectProductId: number }[];
  exchangeProceeds: {
    projectProduct: ProjectProductInterface | null;
    projectItem: ProjectItemInterface | null;
  }[];
};

export type SchemeExchangesDataType = {
  count: number;
  name: string;
  purchases: {
    projectProductId: number;
    quantity: number;
    price: number;
  }[];
  exchanges: {
    projectProductId: number | null;
    projectItemId: number | null;
    quantity: number;
  }[];
};
export type DataType = {
  schemeExchangesData: SchemeExchangesDataType[];
  orderData: (string | number | Date | null | undefined)[];
  prizes?: { name: string; quantity: number; id: number }[];
  samplings?: { name: string; quantity: number; id: number }[];
};
export type ConditionColumnType = {
  columnName: string;
  projectProductId: number;
  schemeName: string;
};
export type ProceedsColumnType = {
  columnName: string;
  projectProductId: number | null;
  projectItemId: number | null;
  schemeName: string;
};
export type OtherStatisticsColumnType = {
  columnName: string;
  schemeName: string;
  statisticName: string;
};

/**
 * Cập nhật dữ liệu cho scheme quà tổng hợp
 * @param featureSchemeExchangesTotal
 * @param exchangeConditions
 * @param exchangeProceeds
 * @param schemeName
 */
export const updateFeatureSchemeExchangeTotal = (
  featureSchemeExchangesTotal: FeatureSchemeExchangeTotalType[],
  exchangeConditions: { projectProductId: number }[],
  exchangeProceeds: ExchangeProceedInterface[],
  schemeName: string,
) => {
  const featureSchemeExchangeTotal = featureSchemeExchangesTotal.find((item) =>
    _.isEqual(item.name, schemeName),
  );
  if (featureSchemeExchangeTotal) {
    featureSchemeExchangeTotal.exchangeConditions = _.uniqBy(
      [
        ...featureSchemeExchangeTotal.exchangeConditions,
        ...exchangeConditions.map((item) => ({
          projectProductId: item.projectProductId,
        })),
      ],
      (o) => o.projectProductId,
    );

    featureSchemeExchangeTotal.exchangeProceeds = _.uniqBy(
      [
        ...featureSchemeExchangeTotal.exchangeProceeds,
        ...exchangeProceeds.map((item) => ({
          projectItem: item.projectItem,
          projectProduct: item.projectProduct,
        })),
      ],
      (o) => o.projectProduct?.id ?? o.projectItem?.id,
    );
  } else {
    featureSchemeExchangesTotal.push({
      name: schemeName,
      exchangeConditions: exchangeConditions.map((item) => ({
        projectProductId: item.projectProductId,
      })),
      exchangeProceeds: exchangeProceeds.map((item) => ({
        projectItem: item.projectItem,
        projectProduct: item.projectProduct,
      })),
    });
  }
};

/**
 * Xử lý dữ liệu cho scheme có điều kiện logic "or"
 * @param schemeName
 * @param schemeExchangesData
 * @param exchangeConditions
 * @param exchangeProceeds
 * @param recordOrderPurchases
 * @param orderQuantity
 */
export const processOrLogicalExchange = (
  schemeName: string,
  schemeExchangesData: SchemeExchangesDataType[],
  exchangeConditions: ExchangeConditionInterface[],
  exchangeProceeds: ExchangeProceedInterface[],
  recordOrderPurchases: RecordOrderPurchaseInterface[],
  orderQuantity: number,
) => {
  const purchases = [];
  let conditionQuantity = exchangeConditions[0]?.quantity * orderQuantity;

  const purchaseProjectProductIds = _.intersection(
    exchangeConditions.map((item) => item.projectProductId),
    recordOrderPurchases?.map(
      (item) => item.featureOrderProduct.projectProductId,
    ),
  );

  for (const purchaseProjectProductId of purchaseProjectProductIds) {
    const recordOrderPurchase = recordOrderPurchases?.find(
      (item) =>
        item.featureOrderProduct.projectProductId === purchaseProjectProductId,
    );

    if (recordOrderPurchase) {
      if (conditionQuantity >= recordOrderPurchase.quantity) {
        conditionQuantity -= recordOrderPurchase.quantity;
        purchases.push({
          projectProductId: purchaseProjectProductId,
          quantity: recordOrderPurchase.quantity,
          price: recordOrderPurchase.featureOrderProduct.price,
        });
        recordOrderPurchase.quantity = 0;
      } else {
        recordOrderPurchase.quantity -= conditionQuantity;
        purchases.push({
          projectProductId: purchaseProjectProductId,
          quantity: conditionQuantity,
          price: recordOrderPurchase.featureOrderProduct.price,
        });
        conditionQuantity = 0;
      }
    }
  }

  schemeExchangesData.push({
    count: orderQuantity,
    name: schemeName,
    exchanges: exchangeProceeds.map((item) => ({
      projectProductId: item.projectProductId,
      projectItemId: item.projectItemId,
      quantity: item.quantity * orderQuantity,
    })),
    purchases,
  });
};

/**
 * Xử lý dữ liệu cho scheme có điều kiện logic "and"
 * @param schemeName
 * @param schemeExchangesData
 * @param exchangeConditions
 * @param exchangeProceeds
 * @param recordOrderPurchases
 * @param orderQuantity
 */
export const processAndLogicalExchange = (
  schemeName: string,
  schemeExchangesData: SchemeExchangesDataType[],
  exchangeConditions: ExchangeConditionInterface[],
  exchangeProceeds: ExchangeProceedInterface[],
  recordOrderPurchases: RecordOrderPurchaseInterface[],
  orderQuantity: number,
) => {
  const purchases = [];

  for (const exchangeCondition of exchangeConditions) {
    const recordOrderPurchase = recordOrderPurchases?.find(
      (item) =>
        item.featureOrderProduct.projectProductId ===
        exchangeCondition.projectProductId,
    );
    if (recordOrderPurchase) {
      purchases.push({
        projectProductId: exchangeCondition.projectProductId,
        quantity: exchangeCondition.quantity * orderQuantity,
        price: recordOrderPurchase.featureOrderProduct.price,
      });
      recordOrderPurchase.quantity -=
        exchangeCondition.quantity * orderQuantity;
    }
  }

  schemeExchangesData.push({
    count: orderQuantity,
    name: schemeName,
    exchanges: exchangeProceeds.map((item) => ({
      projectProductId: item.projectProductId,
      projectItemId: item.projectItemId,
      quantity: item.quantity * orderQuantity,
    })),
    purchases,
  });
};

/**
 * Xử lý dữ liệu cho các sản phẩm mua trong scheme bị dư
 * @param schemeExchangesData
 * @param recordOrderPurchases
 */
export const processPurchasesRedundancy = (
  schemeExchangesData: SchemeExchangesDataType[],
  recordOrderPurchases: RecordOrderPurchaseInterface[],
) => {
  for (const recordOrderPurchase of recordOrderPurchases ?? []) {
    if (recordOrderPurchase.quantity > 0) {
      for (const schemeExchangeData of schemeExchangesData) {
        const schemeExchangeDataPurchase = schemeExchangeData.purchases.find(
          (item) =>
            item.projectProductId ===
            recordOrderPurchase.featureOrderProduct.projectProductId,
        );
        if (schemeExchangeDataPurchase) {
          schemeExchangeDataPurchase.quantity += recordOrderPurchase.quantity;
          break;
        }
      }
    }
  }
};

/**
 * Xử lý dữ liệu cho các sản phẩm mua không thuộc scheme
 * @param schemeExchangesData
 * @param recordOrderPurchases
 * @param featureSchemeExchangesTotal
 */
export const processPurchasesWithoutExchange = (
  schemeExchangesData: SchemeExchangesDataType[],
  recordOrderPurchases: RecordOrderPurchaseInterface[],
  featureSchemeExchangesTotal: FeatureSchemeExchangeTotalType[],
) => {
  const purchases: {
    projectProductId: number;
    quantity: number;
    price: number;
  }[] = [];
  for (const recordOrderPurchase of recordOrderPurchases ?? []) {
    const { quantity, featureOrderProduct } = recordOrderPurchase;
    if (quantity > 0) {
      purchases.push({
        projectProductId: featureOrderProduct.projectProductId,
        quantity,
        price: featureOrderProduct.price,
      });
    }
  }
  if (purchases.length > 0) {
    schemeExchangesData.push({
      name: NO_SCHEME_EXCHANGE_NAME,
      count: 0,
      exchanges: [],
      purchases,
    });

    updateFeatureSchemeExchangeTotal(
      featureSchemeExchangesTotal,
      purchases,
      [],
      NO_SCHEME_EXCHANGE_NAME,
    );
  }
};

/**
 * Render excel header
 * @param worksheet
 * @param fixedHeaders
 * @param featureSchemeExchangesTotal
 * @param projectProducts
 * @param totalData
 * @param conditionColumns
 * @param proceedsColumns
 * @param otherStatisticsColumns
 */

interface RenderExcelHeaderOptionsInterface {
  worksheet: Excel.Worksheet;
  fixedHeaders: string[];
  featureSchemeExchangesTotal: FeatureSchemeExchangeTotalType[];
  projectProducts: ProjectProductInterface[];
  totalData: number;
  conditionColumns: ConditionColumnType[];
  proceedsColumns: ProceedsColumnType[];
  otherStatisticsColumns: OtherStatisticsColumnType[];
  luckyDrawsHeaders: {
    name: string;
    id: number;
    ordinal: number;
    columnName?: string;
  }[];
  samplingsHeaders: {
    name: string;
    id: number;
    ordinal: number;
    columnName?: string;
  }[];
}
export const renderExcelHeader = ({
  worksheet,
  fixedHeaders,
  featureSchemeExchangesTotal,
  projectProducts,
  totalData,
  conditionColumns,
  proceedsColumns,
  otherStatisticsColumns,
  luckyDrawsHeaders,
  samplingsHeaders,
}: RenderExcelHeaderOptionsInterface) => {
  worksheet.insertRow(1, [...fixedHeaders]);
  for (const [i] of fixedHeaders.entries()) {
    const address = `${numToCol(i + 1)}1:${numToCol(i + 1)}5`;
    worksheet.mergeCells(address);
    formatCellHeader(worksheet.getCell(address));
  }

  const totalCellAddress = `${numToCol(1)}6:${numToCol(fixedHeaders.length)}6`;
  worksheet.mergeCells(totalCellAddress);
  const totalCell = worksheet.getCell(totalCellAddress);
  formatCellHeader(totalCell, { horizontal: "right" });
  totalCell.value = "Tổng";

  let currentColumnIndex = fixedHeaders.length + 1;
  for (const featureSchemeExchangeTotal of featureSchemeExchangesTotal) {
    const {
      name: schemeName,
      exchangeProceeds,
      exchangeConditions,
    } = featureSchemeExchangeTotal;

    const schemeExchangeHeaderLength =
      exchangeProceeds.length +
      exchangeConditions.length +
      OTHER_STATISTICS_COLUMNS.length;

    worksheet.getCell(`${numToCol(currentColumnIndex)}1`).value = schemeName;

    const address = `${numToCol(currentColumnIndex)}1:${numToCol(currentColumnIndex + schemeExchangeHeaderLength - 1)}1`;
    worksheet.mergeCells(address);
    formatCellHeader(worksheet.getCell(address));

    /**
     * Sản phẩm mua
     */
    if (exchangeConditions.length > 0) {
      const exchangeConditionsPosition = currentColumnIndex;

      worksheet.getCell(`${numToCol(exchangeConditionsPosition)}2`).value =
        "Sản phẩm mua";
      const columnAddress = `${numToCol(exchangeConditionsPosition)}2:${numToCol(exchangeConditionsPosition + exchangeConditions.length - 1)}2`;

      worksheet.mergeCells(columnAddress);
      formatCellHeader(worksheet.getCell(columnAddress));

      const exchangeConditionsProjectProducts = _.filter(projectProducts, (o) =>
        exchangeConditions.map((item) => item.projectProductId).includes(o.id),
      );

      const exchangeConditionsProjectProductsBrands = _.groupBy(
        exchangeConditionsProjectProducts,
        (o) => o.product.brand?.name,
      );

      let currentColumnIndexInSchemeExchange = exchangeConditionsPosition;
      for (const [brandName, projectProducts] of Object.entries(
        exchangeConditionsProjectProductsBrands,
      )) {
        worksheet.getCell(
          `${numToCol(currentColumnIndexInSchemeExchange)}3`,
        ).value = brandName;
        const columnAddress = `${numToCol(currentColumnIndexInSchemeExchange)}3:${numToCol(currentColumnIndexInSchemeExchange + projectProducts.length - 1)}3`;
        worksheet.mergeCells(columnAddress);
        formatCellHeader(worksheet.getCell(columnAddress));

        for (const [i, projectProduct] of projectProducts.entries()) {
          const { projectProductPrices, product } = projectProduct;
          const columnName = numToCol(currentColumnIndexInSchemeExchange + i);

          const productCell = worksheet.getCell(`${columnName}4`);
          productCell.value = product.name;
          formatCellHeader(productCell);

          const priceCell = worksheet.getCell(`${columnName}5`);
          priceCell.value =
            projectProductPrices && projectProductPrices?.length > 0
              ? projectProductPrices[0]?.price
              : "";
          priceCell.numFmt = `#,##0`;
          formatCellHeader(priceCell, { horizontal: "right" });

          const totalCell = worksheet.getCell(`${columnName}6`);
          totalCell.value = {
            formula: `SUM(${columnName}7:${columnName}${totalData + 6})`,
          };
          totalCell.numFmt = `#,##0`;
          formatCellHeader(totalCell, { horizontal: "right" });

          conditionColumns.push({
            columnName: columnName,
            projectProductId: projectProduct.id,
            schemeName,
          });
        }

        currentColumnIndexInSchemeExchange += projectProducts.length;
      }
    }

    /**
     * Quà đã nhận
     */
    if (exchangeProceeds.length > 0) {
      const exchangeProceedsPosition =
        currentColumnIndex + exchangeConditions.length;

      worksheet.getCell(`${numToCol(exchangeProceedsPosition)}2`).value =
        "Quà đã nhận";

      const columnAddress = `${numToCol(exchangeProceedsPosition)}2:${numToCol(exchangeProceedsPosition + exchangeProceeds.length - 1)}3`;
      worksheet.mergeCells(columnAddress);
      formatCellHeader(worksheet.getCell(columnAddress));

      for (const [i, exchangeProceed] of exchangeProceeds.entries()) {
        const columnName = numToCol(exchangeProceedsPosition + i);
        const cell = worksheet.getCell(`${columnName}4`);
        cell.value =
          exchangeProceed.projectItem?.item?.name ??
          exchangeProceed.projectProduct?.product?.name;
        formatCellHeader(cell);

        const priceCell = worksheet.getCell(`${columnName}5`);
        formatCellHeader(priceCell);

        const totalCell = worksheet.getCell(`${columnName}6`);
        totalCell.value = {
          formula: `SUM(${columnName}7:${columnName}${totalData + 6})`,
        };
        totalCell.numFmt = `#,##0`;
        formatCellHeader(totalCell, { horizontal: "right" });

        proceedsColumns.push({
          schemeName,
          columnName: columnName,
          projectProductId: exchangeProceed.projectProduct?.id ?? null,
          projectItemId: exchangeProceed.projectItem?.id ?? null,
        });
      }
    }

    /**
     * Thống kê khác
     */
    if (OTHER_STATISTICS_COLUMNS.length > 0) {
      const otherStatisticsPosition =
        currentColumnIndex +
        exchangeConditions.length +
        exchangeProceeds.length;

      worksheet.getCell(`${numToCol(otherStatisticsPosition)}2`).value =
        "Thống kê khác";
      const address = `${numToCol(otherStatisticsPosition)}2:${numToCol(otherStatisticsPosition + OTHER_STATISTICS_COLUMNS.length - 1)}3`;
      worksheet.mergeCells(address);
      formatCellHeader(worksheet.getCell(address));

      for (const [
        i,
        otherStatisticsColumn,
      ] of OTHER_STATISTICS_COLUMNS.entries()) {
        const columnName = numToCol(otherStatisticsPosition + i);

        const cell = worksheet.getCell(`${columnName}4`);
        cell.value = otherStatisticsColumn;
        formatCellHeader(cell);

        const priceCell = worksheet.getCell(`${columnName}5`);
        formatCellHeader(priceCell);

        const totalCell = worksheet.getCell(`${columnName}6`);
        totalCell.value = {
          formula: `SUM(${columnName}7:${columnName}${totalData + 6})`,
        };
        totalCell.numFmt = `#,##0`;
        formatCellHeader(totalCell, { horizontal: "right" });

        otherStatisticsColumns.push({
          schemeName,
          columnName,
          statisticName: otherStatisticsColumn,
        });
      }
    }

    currentColumnIndex += schemeExchangeHeaderLength;
  }

  if (luckyDrawsHeaders.length) {
    luckyDrawsHeaders.sort((a, b) => a.ordinal - b.ordinal);
    {
      worksheet.getCell(`${numToCol(currentColumnIndex)}1`).value =
        "Lucky draw";
      const address = `${numToCol(currentColumnIndex)}1:${numToCol(currentColumnIndex + luckyDrawsHeaders.length - 1)}1`;
      worksheet.mergeCells(address);
      formatCellHeader(worksheet.getCell(address));
    }

    {
      worksheet.getCell(`${numToCol(currentColumnIndex)}2`).value =
        "Quà nhận được";
      const address = `${numToCol(currentColumnIndex)}2:${numToCol(currentColumnIndex + luckyDrawsHeaders.length - 1)}3`;
      worksheet.mergeCells(address);
      formatCellHeader(worksheet.getCell(address));
    }

    for (const luckyDrawHeader of luckyDrawsHeaders) {
      const { name } = luckyDrawHeader;

      const columnName = numToCol(currentColumnIndex);
      const cell = worksheet.getCell(`${columnName}4`);
      cell.value = name;
      formatCellHeader(cell);

      const totalCell = worksheet.getCell(`${columnName}6`);
      totalCell.value = {
        formula: `SUM(${columnName}7:${columnName}${totalData + 6})`,
      };
      totalCell.numFmt = `#,##0`;
      formatCellHeader(totalCell, { horizontal: "right" });

      currentColumnIndex++;
      luckyDrawHeader.columnName = columnName;
    }
  }

  if (samplingsHeaders.length) {
    samplingsHeaders.sort((a, b) => a.ordinal - b.ordinal);
    {
      worksheet.getCell(`${numToCol(currentColumnIndex)}1`).value = "Sampling";
      const address = `${numToCol(currentColumnIndex)}1:${numToCol(currentColumnIndex + samplingsHeaders.length - 1)}1`;
      worksheet.mergeCells(address);
      formatCellHeader(worksheet.getCell(address));
    }

    {
      worksheet.getCell(`${numToCol(currentColumnIndex)}2`).value =
        "Sampling nhận được";
      const address = `${numToCol(currentColumnIndex)}2:${numToCol(currentColumnIndex + samplingsHeaders.length - 1)}3`;
      worksheet.mergeCells(address);
      formatCellHeader(worksheet.getCell(address));
    }

    for (const samplingHeader of samplingsHeaders) {
      const { name } = samplingHeader;

      const columnName = numToCol(currentColumnIndex);
      const cell = worksheet.getCell(`${columnName}4`);
      cell.value = name;
      formatCellHeader(cell);

      const totalCell = worksheet.getCell(`${columnName}6`);
      totalCell.value = {
        formula: `SUM(${columnName}7:${columnName}${totalData + 6})`,
      };
      totalCell.numFmt = `#,##0`;
      formatCellHeader(totalCell, { horizontal: "right" });

      currentColumnIndex++;
      samplingHeader.columnName = columnName;
    }
  }
};

/**
 * Fill dữ liệu cho excel
 * @param worksheet
 * @param data
 * @param BEGIN_ROW
 * @param conditionColumns
 * @param proceedsColumns
 * @param otherStatisticsColumns
 */
export const fillDataExcel = (
  worksheet: Excel.Worksheet,
  data: DataType[],
  BEGIN_ROW: number,
  conditionColumns: ConditionColumnType[],
  proceedsColumns: ProceedsColumnType[],
  otherStatisticsColumns: OtherStatisticsColumnType[],
  luckyDrawsHeaders: {
    name: string;
    id: number;
    ordinal: number;
    columnName?: string;
  }[],
  samplingsHeaders: {
    name: string;
    id: number;
    ordinal: number;
    columnName?: string;
  }[],
) => {
  const groupConditionColumnsBySchemeName = _.groupBy(
    conditionColumns,
    (o) => o.schemeName,
  );
  const groupProceedsColumnsBySchemeName = _.groupBy(
    proceedsColumns,
    (o) => o.schemeName,
  );
  const groupOtherStatisticsColumnsBySchemeName = _.groupBy(
    otherStatisticsColumns,
    (o) => o.schemeName,
  );

  for (const [
    i,
    { schemeExchangesData, orderData, prizes, samplings },
  ] of data.entries()) {
    const rowIndex = BEGIN_ROW + i;
    worksheet.insertRow(rowIndex, [...orderData]);
    const DATE_TIME_COLUMNS = ["C", "D", "M"];
    for (const column of DATE_TIME_COLUMNS) {
      worksheet.getCell(`${column}${rowIndex}`).numFmt = "dd/mm/yyyy hh:mm:ss";
    }

    for (const schemeExchangeData of schemeExchangesData) {
      for (const [schemeName, conditionColumns] of Object.entries(
        groupConditionColumnsBySchemeName,
      )) {
        if (_.isEqual(schemeName, schemeExchangeData.name)) {
          for (const conditionColumn of conditionColumns) {
            for (const item of schemeExchangeData.purchases) {
              if (item.projectProductId === conditionColumn.projectProductId) {
                const cell = worksheet.getCell(
                  `${conditionColumn.columnName}${rowIndex}`,
                );
                if (Number(cell.value) > 0) {
                  cell.value = item.quantity + Number(cell.value);
                } else {
                  cell.value = item.quantity;
                }
              }
            }
          }
        }
      }

      for (const [schemeName, proceedsColumns] of Object.entries(
        groupProceedsColumnsBySchemeName,
      )) {
        if (_.isEqual(schemeName, schemeExchangeData.name)) {
          for (const proceedsColumn of proceedsColumns) {
            for (const item of schemeExchangeData.exchanges) {
              if (
                item.projectProductId === proceedsColumn.projectProductId &&
                item.projectItemId === proceedsColumn.projectItemId
              ) {
                const cell = worksheet.getCell(
                  `${proceedsColumn.columnName}${rowIndex}`,
                );
                if (Number(cell.value) > 0) {
                  cell.value = item.quantity + Number(cell.value);
                } else {
                  cell.value = item.quantity;
                }
              }
            }
          }
        }
      }
      for (const [schemeName, otherStatisticsColumns] of Object.entries(
        groupOtherStatisticsColumnsBySchemeName,
      )) {
        if (_.isEqual(schemeName, schemeExchangeData.name)) {
          for (const otherStatisticsColumn of otherStatisticsColumns) {
            if (
              _.isEqual(otherStatisticsColumn.statisticName, STATISTIC_PURCHASE)
            ) {
              const cell = worksheet.getCell(
                `${otherStatisticsColumn.columnName}${rowIndex}`,
              );
              if (Number(cell.value) > 0) {
                cell.value = schemeExchangeData.count + Number(cell.value);
              } else {
                cell.value = schemeExchangeData.count;
              }
            }

            if (
              _.isEqual(otherStatisticsColumn.statisticName, STATISTIC_REVENUE)
            ) {
              let revenue = 0;

              for (const item of schemeExchangeData.purchases) {
                revenue += item.price * item.quantity;
              }
              const cell = worksheet.getCell(
                `${otherStatisticsColumn.columnName}${rowIndex}`,
              );

              if (Number(cell.value) > 0) {
                cell.value = revenue + Number(cell.value);
              } else {
                cell.value = revenue;
              }

              cell.numFmt = `#,##0`;
            }
          }
        }
      }
    }

    for (const luckyDrawHeader of luckyDrawsHeaders) {
      const cell = worksheet.getCell(
        `${luckyDrawHeader.columnName}${rowIndex}`,
      );
      cell.value = prizes?.find((p) => p.id === luckyDrawHeader.id)?.quantity;
    }

    for (const samplingHeader of samplingsHeaders) {
      const cell = worksheet.getCell(`${samplingHeader.columnName}${rowIndex}`);
      cell.value = samplings?.find((p) => p.id === samplingHeader.id)?.quantity;
    }
  }
};
