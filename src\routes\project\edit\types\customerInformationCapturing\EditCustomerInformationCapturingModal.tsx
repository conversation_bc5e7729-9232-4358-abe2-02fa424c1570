import { CURD } from "@/common/constant.ts";
import { useFormPhotosStore } from "@/components/formImageUploadComponent/state.ts";
import { CloseOutlined } from "@ant-design/icons";
import { OrderProductInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { Button, Col, Form, Modal, Row } from "antd";
import dayjs from "dayjs";
import { useCallback, useState } from "react";
import {
  useCreateCustomerInformationMutation,
  useEditOrderQuery,
  useUpdateCustomerInformationMutation,
} from "./service.ts";
import { EditCustomerInformationCapturingModalProps } from "./types.ts";

const EditCustomerInformationCapturingModal = ({
  action,
  isOpen,
  componentFeatureId,
  projectId,
  attendanceId,
  cancelCb,
  componentFeature,
  orderId,
  attendance,
}: EditCustomerInformationCapturingModalProps) => {
  // Form and state management
  const [form] = Form.useForm();
  const [selectedOrderProducts, setSelectedOrderProducts] = useState<
    OrderProductInterface[]
  >([]);

  const { formPhotos: photos, setFormPhotos: setPhotos } = useFormPhotosStore();

  // API queries and mutations
  const editOrderQuery = useEditOrderQuery(
    projectId,
    attendanceId,
    componentFeatureId,
    orderId,
  );

  const createCustomerInformationMutation =
    useCreateCustomerInformationMutation(
      projectId,
      attendanceId,
      componentFeatureId,
    );
  const updateCustomerInformationMutation =
    useUpdateCustomerInformationMutation(
      projectId,
      attendanceId,
      componentFeatureId,
    );

  // Custom hooks
  const {
    featureCustomers,
    featureOrder,
    featureOrderProducts,
    featureSchemes,
    featurePhotos,
    isIdentity,
  } = useOrderData(
    componentFeature,
    editOrderQuery,
    form,
    setPhotos,
    setSelectedOrderProducts,
  );

  const { selectedExchangeItems } = useExchangeItems(
    featureSchemes,
    selectedOrderProducts,
  );

  const { onSubmit, loading } = useFormSubmission(
    form,
    action,
    featureCustomers,
    featurePhotos,
    photos,
    selectedOrderProducts,
    selectedExchangeItems,
    createCustomerInformationMutation,
    updateCustomerInformationMutation,
    editOrderQuery,
    () => resetFormState(form, setPhotos, cancelCb),
  );

  // Event handlers
  const onPurchaseSelected = useCallback(
    (orderProductId: number) => {
      handlePurchaseSelection(
        orderProductId,
        featureOrderProducts,
        setSelectedOrderProducts,
        form,
      );
    },
    [featureOrderProducts, form],
  );

  const deleteOrderProduct = useCallback((id: number) => {
    handleProductDeletion(id, setSelectedOrderProducts);
  }, []);

  return (
    <Modal
      open={isOpen}
      footer={false}
      closeIcon={null}
      styles={{ content: { padding: MODAL_CONFIG.PADDING } }}
      width={MODAL_CONFIG.WIDTH}
    >
      <Row justify={"space-between"} className="">
        <Col md={24} className="pl-10 pt-5">
          <div className={CSS_CLASSES.HEADER}>
            <h2 className={CSS_CLASSES.HEADER_TITLE}>
              {action === CURD.CREATE
                ? UI_TEXT.MODAL_TITLE_CREATE
                : UI_TEXT.MODAL_TITLE_UPDATE}
            </h2>
            <div className="pt-5">
              <Button
                loading={loading}
                type="link"
                onClick={() => resetFormState(form, setPhotos, cancelCb)}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <div className={CSS_CLASSES.SCROLL_CONTAINER}>
            <Form
              layout="vertical"
              form={form}
              initialValues={getInitialFormValues(attendance)}
            >
              <CustomerSection
                featureOrder={featureOrder}
                featureCustomers={featureCustomers}
                action={action}
                isIdentity={isIdentity}
              />

              <PurchaseSection
                featureOrder={featureOrder}
                selectedOrderProducts={selectedOrderProducts}
                featureOrderProducts={featureOrderProducts}
                onPurchaseSelected={onPurchaseSelected}
                deleteOrderProduct={deleteOrderProduct}
              />

              <ExchangeSection
                featureOrder={featureOrder}
                selectedExchangeItems={selectedExchangeItems}
              />

              <PhotoSection
                featureOrder={featureOrder}
                featurePhotos={featurePhotos}
                editOrderQuery={editOrderQuery}
              />

              <TimeSection
                action={action}
                attendance={attendance}
              />
            </Form>
          </div>
        </Col>
      </Row>

      <div className={CSS_CLASSES.FOOTER}>
        <Button
          type="default"
          onClick={() => resetFormState(form, setPhotos, cancelCb)}
          loading={loading}
        >
          {UI_TEXT.CLOSE_BUTTON}
        </Button>
        <Button
          htmlType="submit"
          type={"primary"}
          onClick={onSubmit}
          loading={loading}
        >
          {action === CURD.CREATE ? UI_TEXT.CREATE_BUTTON : UI_TEXT.UPDATE_BUTTON}
        </Button>
      </div>
    </Modal>
  );
};

  const renderedExchangeProceeds = useCallback(
    (exchangeProceeds: ExchangeProceedInterface[]) => {
      return exchangeProceeds.map((exchangeProceed, index) => {
        const { projectItem, projectProduct, quantity } = exchangeProceed;
        const itemName =
          projectItem?.item?.name ?? projectProduct?.product?.name;
        const unitName =
          projectProduct?.productPackaging?.unit?.name ??
          projectItem?.item.unit?.name;
        const code = projectProduct?.product?.code ?? projectItem?.item?.code;

        return (
          <p
            key={`${index}-${exchangeProceed.id}-${projectProduct?.id}-${projectItem?.id}`}
            className="mt-0 mb-0"
          >
            x{quantity} {unitName} - {code} - {itemName}
          </p>
        );
      });
    },
    [],
  );

  const selectedExchangeItems = useMemo(
    () =>
      featureSchemes.flatMap((featureScheme) => {
        const { featureSchemeExchanges } = featureScheme;

        return featureSchemeExchanges
          .filter((item) => !item.luckyDrawId)
          .flatMap((featureSchemeExchange) => {
            const { logical, exchangeConditions, id, exchangeProceeds, name } =
              featureSchemeExchange;

            if (exchangeConditions.length === 0) {
              return {
                item: (
                  <Form.Item
                    name={id}
                    label={
                      <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                    }
                  >
                    <InputNumber className="w-full" controls={false} />
                  </Form.Item>
                ),
                schemeName: name,
                id: id,
              };
            }

            if (logical === "or") {
              const hasProjectProduct = exchangeConditions.some(
                (exchangeCondition) =>
                  selectedOrderProducts.some(
                    (orderProduct) =>
                      orderProduct.projectProductId ===
                      exchangeCondition.projectProductId,
                  ),
              );

              if (hasProjectProduct) {
                return {
                  item: (
                    <Form.Item
                      name={id}
                      label={
                        <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                      }
                    >
                      <InputNumber className="w-full" controls={false} />
                    </Form.Item>
                  ),
                  schemeName: name,
                  id: id,
                };
              }
            }

            if (logical === "and") {
              const selectedOrderProductsProjectProductId =
                selectedOrderProducts
                  .map((item) => item.projectProductId)
                  .sort((a, b) => a - b);
              const exchangeConditionsProjectProductId = exchangeConditions
                .map((item) => item.projectProductId)
                .sort((a, b) => a - b);

              if (
                _.intersection(
                  selectedOrderProductsProjectProductId,
                  exchangeConditionsProjectProductId,
                ).length === exchangeConditionsProjectProductId.length
              ) {
                return {
                  item: (
                    <Form.Item
                      name={id}
                      label={
                        <div>{renderedExchangeProceeds(exchangeProceeds)}</div>
                      }
                    >
                      <InputNumber className="w-full" controls={false} />
                    </Form.Item>
                  ),
                  schemeName: name,
                  id: id,
                };
              }
            }
          });
      }),
    [featureSchemes, renderedExchangeProceeds, selectedOrderProducts],
  );

  const onSubmit = useCallback(async () => {
    setLoading(true);

    try {
      const data = await form.validateFields();

      const { customers, time, purchases, exchanges } = data;
      const photoFiles: { fileList: UploadFile[] }[] = data.photoFiles ?? {};

      /**
       * Validate photos
       */
      for (const [featurePhotoId, file] of Object.entries(photoFiles ?? {})) {
        const featurePhoto = featurePhotos.find(
          (featurePhoto) => featurePhoto.id === Number(featurePhotoId),
        );
        if (!featurePhoto) {
          throw new Error(`Feature photo ${featurePhotoId} not found`);
        }

        const alreadyUploadedPhotos = photos.filter(
          (photo) => photo.type === featurePhotoId,
        );

        const length =
          (file?.fileList?.filter((file) => file.status !== "done").length ??
            0) + alreadyUploadedPhotos.length;

        if (length < featurePhoto.minimum || length > featurePhoto.maximum) {
          throw new Error(
            `${featurePhoto.name} phải có số lượng giữa ${featurePhoto.minimum} và ${featurePhoto.maximum}`,
          );
        }
      }

      const photosData = [];
      for (const [featurePhotoId, photoFile] of Object.entries(
        photoFiles ?? {},
      )) {
        if (photoFile) {
          const { fileList } = photoFile;
          for (const file of fileList) {
            if (file.originFileObj) {
              const result = await uploadImageMutation.mutateAsync(
                file.originFileObj,
              );
              if (result?.id)
                photosData.push({
                  featurePhotoId: Number(featurePhotoId),
                  imageId: result.id,
                  dataUuid: uuidv4(),
                  dataTimestamp: time.toISOString(),
                });
            }
          }
        }
      }
      for (const photo of photos) {
        const { image, dataUuid, dataTimestamp } = photo;

        photosData.push({
          featurePhotoId: Number(photo.type),
          imageId: image.id,
          dataUuid: dataUuid ?? "",
          dataTimestamp: dataTimestamp ?? "",
        });
      }

      const customersData = [];
      for (const [featureCustomerId, value] of Object.entries(
        customers ?? {},
      )) {
        if (typeof value === "number") {
          customersData.push({
            featureCustomerId: Number(featureCustomerId),
            featureCustomerOptionIds: [value],
          });
        }
        if (typeof value === "string") {
          customersData.push({
            featureCustomerId: Number(featureCustomerId),
            value: String(value),
          });
        }
      }
      if (customersData.length !== featureCustomers.length) {
        for (const featureCustomer of featureCustomers) {
          if (
            !customersData.find(
              (customer) => customer.featureCustomerId === featureCustomer.id,
            )
          ) {
            customersData.push({
              featureCustomerId: featureCustomer.id,
              value: null,
            });
          }
        }
      }

      const purchasesData = [];
      for (const [featureOrderProductId, quantity] of Object.entries(
        purchases ?? {},
      )) {
        if (Number(quantity)) {
          const selectedOrderProduct = selectedOrderProducts.find(
            (selectedOrderProduct) =>
              selectedOrderProduct.id === Number(featureOrderProductId),
          );

          if (selectedOrderProduct) {
            purchasesData.push({
              featureOrderProductId: Number(featureOrderProductId),
              quantity: Number(quantity),
            });
          }
        }
      }

      const exchangesData = [];
      for (const [featureSchemeExchangeId, quantity] of Object.entries(
        exchanges ?? {},
      )) {
        if (Number(quantity)) {
          const groupedItem = selectedExchangeItems.find(
            (groupedItem) =>
              groupedItem?.id === Number(featureSchemeExchangeId),
          );
          if (groupedItem) {
            exchangesData.push({
              featureSchemeExchangeId: Number(featureSchemeExchangeId),
              quantity: Number(quantity),
            });
          }
        }
      }

      switch (action) {
        case CURD.CREATE:
          await createCustomerInformationMutation.mutateAsync({
            customers: customersData,
            dataTimestamp: dayjs(time)
              .add(_.random(59 - dayjs(time).second()), "second")
              .toISOString(),
            exchanges: exchangesData,
            purchases: purchasesData,
            photos: photosData,
            dataUuid: uuidv4(),
          });

          showNotification({
            type: "success",
            message: "Tạo đơn hàng thành công.",
          });

          break;
        case CURD.UPDATE:
          await updateCustomerInformationMutation.mutateAsync({
            id: editOrderQuery.data?.id ?? 0,
            data: {
              customers: customersData,
              dataTimestamp: time.toISOString(),
              exchanges: exchangesData,
              purchases: purchasesData,
              photos: photosData,
            },
          });
          showNotification({
            type: "success",
            message: "Cập nhật đơn hàng thành công.",
          });
          break;
      }

      onClose();

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      showNotification({
        type: "error",
        message: (error.message as unknown as string) ?? "Có lỗi xảy ra",
      });
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [
    form,
    featureCustomers,
    action,
    onClose,
    featurePhotos,
    photos,
    uploadImageMutation,
    selectedOrderProducts,
    selectedExchangeItems,
    createCustomerInformationMutation,
    showNotification,
    updateCustomerInformationMutation,
    editOrderQuery.data?.id,
  ]);

  return (
    <Modal
      open={isOpen}
      footer={false}
      closeIcon={null}
      styles={{ content: { padding: 0 } }}
      width={708}
    >
      <Row justify={"space-between"} className="">
        <Col md={24} className="pl-10 pt-5">
          <div className="flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap mr-10">
            <h2 className="text-neutral-700 text-2xl font-semibold ">
              {action === CURD.CREATE
                ? "Thêm mới đơn hàng"
                : "Chỉnh sửa đơn hàng"}
            </h2>
            <div className="pt-5">
              <Button
                loading={loading}
                type="link"
                onClick={onClose}
                size="large"
                icon={<CloseOutlined />}
              />
            </div>
          </div>
          <div className="overflow-y-scroll overflow-x-hidden max-h-[650px] pr-10">
            <Form
              layout="vertical"
              form={form}
              initialValues={{
                time: dayjs(attendance?.createdAt).add(1, "minute"),
              }}
            >
              {featureOrder?.hasCustomer && (
                <>
                  <p
                    className="text-primary font-semibold text-lg"
                    id="customers"
                  >
                    Thông tin khách hàng
                  </p>

                  <Form.List name={"customers"}>
                    {() => {
                      return featureCustomers.map((featureCustomer) => (
                        <CustomerInput
                          key={featureCustomer.id}
                          featureCustomer={featureCustomer}
                        />
                      ));
                    }}
                  </Form.List>
                </>
              )}

              {action === CURD.UPDATE && isIdentity && (
                <>
                  <p
                    className="text-primary font-semibold text-lg"
                    id="customers"
                  >
                    Xác thực
                  </p>

                  <Form.Item label={"OTP"} required name={"otpCode"}>
                    <Input disabled />
                  </Form.Item>
                </>
              )}

              {featureOrder?.hasPurchase && (
                <>
                  <p
                    className="text-primary font-semibold text-lg mt-7"
                    id="purchase"
                  >
                    Sản phẩm đã mua
                  </p>

                  <Form.List name={"purchases"}>
                    {() => {
                      return selectedOrderProducts.map((orderProduct) => {
                        const { projectProduct, id } = orderProduct;
                        const unitName =
                          projectProduct.productPackaging?.unit.name;
                        const { code, name } = projectProduct.product;

                        return (
                          <div
                            className="flex items-center mb-4 w-full"
                            key={`selectedOrderProducts${id}`}
                          >
                            <Form.Item
                              label={`${unitName} - ${code} - ${name}`}
                              name={`${id}`}
                              className={"w-full"}
                            >
                              <InputNumber
                                className={"w-full"}
                                controls={false}
                              />
                            </Form.Item>

                            <Button
                              type="text"
                              icon={<DeleteOutlined />}
                              className="ml-2"
                              onClick={() => {
                                deleteOrderProduct(id);
                              }}
                            />
                          </div>
                        );
                      });
                    }}
                  </Form.List>

                  <Form.Item name={"orderProduct"}>
                    <Select
                      showSearch={true}
                      placeholder={"Chọn sản phẩm cần thêm vào đơn hàng"}
                      filterOption={filterOption}
                      options={featureOrderProducts
                        .filter(
                          (orderProduct) =>
                            !selectedOrderProducts
                              .map((item) => item.id)
                              .includes(orderProduct.id),
                        )
                        .map((orderProduct) => ({
                          label: `${orderProduct.projectProduct.productPackaging?.unit.name} - ${orderProduct.projectProduct.product.code} - ${orderProduct.projectProduct.product.name}`,
                          value: orderProduct.id,
                        }))}
                      onSelect={onPurchaseSelected}
                    />
                  </Form.Item>
                </>
              )}

              {featureOrder?.hasExchange && (
                <>
                  <p
                    className="text-primary font-semibold text-lg mt-7"
                    id="gift"
                  >
                    Quà đã nhận
                  </p>
                  <Form.Item noStyle dependencies={["purchases"]}>
                    {() => (
                      <Form.List name="exchanges">
                        {() => {
                          const filteredItems = selectedExchangeItems.filter(
                            (item) => item,
                          );
                          const groupedBySchemeName = _.groupBy(
                            filteredItems,
                            (item) => item?.schemeName,
                          );

                          return Object.entries(groupedBySchemeName).map(
                            ([schemeName, items]) => {
                              if (schemeName && items.length > 0) {
                                return (
                                  <Fragment key={schemeName}>
                                    <p className="text-blue font-semibold">
                                      {schemeName}
                                    </p>
                                    {items.map((item) => (
                                      <Fragment key={item?.id}>
                                        {item?.item}
                                      </Fragment>
                                    ))}
                                  </Fragment>
                                );
                              }

                              return <></>;
                            },
                          );
                        }}
                      </Form.List>
                    )}
                  </Form.Item>
                </>
              )}

              {featureOrder?.hasPhoto && (
                <>
                  <p
                    className="text-primary font-semibold text-lg pb-0 mb-0 mt-[40px]"
                    id="photoFiles"
                  >
                    Hình ảnh
                  </p>
                  <p className="font-normal text-xs text-hint mt-0 pt-0 mb-5">
                    Vui lòng sử dụng ảnh có định dạng .png, .jpg, .jpeg và có
                    dung lượng &lt;= 5mb
                  </p>

                  {featurePhotos.map((featurePhoto, index) => (
                    <FormImageUploadComponent
                      key={index}
                      label={featurePhoto.name}
                      fieldName={["photoFiles", `${featurePhoto.id}`]}
                      max={featurePhoto.maximum}
                      required={featurePhoto.minimum > 0}
                      imagesFile={editOrderQuery.data?.recordOrderPhotos
                        ?.filter(
                          (recordOrderPhoto) =>
                            recordOrderPhoto.recordPhoto.featurePhotoId ===
                            featurePhoto.id,
                        )
                        .map(
                          (recordOrderPhoto) =>
                            recordOrderPhoto.recordPhoto.image,
                        )}
                    />
                  ))}
                </>
              )}

              <div hidden={action === CURD.UPDATE}>
                <p
                  className="text-primary font-semibold text-lg pb-0 mb-0 mt-[40px]"
                  id="time"
                >
                  Thời gian ghi đơn
                </p>

                <Form.Item
                  label={"Thời gian ghi đơn (giờ 24)"}
                  name={"time"}
                  rules={[{ required: true }]}
                >
                  <TimePicker
                    format={"HH:mm"}
                    className={"w-[50%]"}
                    showNow={false}
                    disabledTime={(date: Dayjs) => {
                      const hours = date.get("hours");

                      const startArrayHours: number[] = [];
                      const startTime = dayjs(attendance?.createdAt);
                      startArrayHours.length = startTime.get("hours");

                      const endTime = dayjs(attendance?.updatedAt);
                      const endArrayHours: number[] = [];
                      endArrayHours.length =
                        24 -
                        (endTime.get("hours") === 0
                          ? 23
                          : endTime.get("hours"));

                      return {
                        disabledHours: () => [
                          ...startArrayHours.fill(0).map((_, index) => index),
                          ...endArrayHours
                            .fill(0)
                            .map((_, index) => 24 - index),
                        ],
                        disabledMinutes: () => {
                          const disabledMinutes: number[] = [];

                          if (hours === startTime.get("hours")) {
                            const startArrayMinutes: number[] = [];
                            startArrayMinutes.length =
                              startTime.get("minutes") + 1; // +1 bắt đầu
                            disabledMinutes.push(
                              ...startArrayMinutes
                                .fill(0)
                                .map((_, index) => index),
                            );
                          }

                          if (hours === endTime.get("hours")) {
                            const endArrayMinutes: number[] = [];
                            endArrayMinutes.length =
                              60 - endTime.get("minutes") + 1; // -1 kết thúc
                            disabledMinutes.push(
                              ...endArrayMinutes
                                .fill(0)
                                .map((_, index) => 60 - index),
                            );
                          }

                          return disabledMinutes;
                        },
                      };
                    }}
                  />
                </Form.Item>
              </div>
            </Form>
          </div>
        </Col>
      </Row>

      <div className="flex justify-end gap-4 py-4 rounded-b max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4 border-t-[1.5px] border-[#DDE1EA] border-solid border-l-0 border-r-0 border-b-0">
        <Button type="default" onClick={onClose} style={{}} loading={loading}>
          Đóng
        </Button>
        <Button
          htmlType="submit"
          type={"primary"}
          onClick={onSubmit}
          loading={loading}
        >
          {action === CURD.CREATE ? "Thêm mới" : "Cập nhật"}
        </Button>
      </div>
    </Modal>
  );
};

export default EditCustomerInformationCapturingModal;
