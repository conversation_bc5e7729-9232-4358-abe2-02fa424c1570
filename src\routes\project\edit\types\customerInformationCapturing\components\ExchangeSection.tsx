import { Form } from "antd";
import _ from "lodash";
import { Fragment } from "react";
import { CSS_CLASSES, FORM_SECTION_IDS, UI_TEXT } from "../constants.ts";
import { ExchangeSectionProps } from "../types.ts";

/**
 * Exchange/gifts section component
 */
const ExchangeSection = ({
  featureOrder,
  selectedExchangeItems,
}: ExchangeSectionProps) => {
  if (!featureOrder?.hasExchange) {
    return null;
  }

  return (
    <Fragment>
      <p
        className={CSS_CLASSES.SECTION_TITLE_WITH_MARGIN}
        id={FORM_SECTION_IDS.GIFT}
      >
        {UI_TEXT.GIFTS_RECEIVED_TITLE}
      </p>
      
      <Form.Item noStyle dependencies={["purchases"]}>
        {() => (
          <Form.List name="exchanges">
            {() => {
              const filteredItems = selectedExchangeItems.filter(
                (item) => item,
              );
              const groupedBySchemeName = _.groupBy(
                filteredItems,
                (item) => item?.schemeName,
              );

              return Object.entries(groupedBySchemeName).map(
                ([schemeName, items]) => {
                  if (schemeName && items.length > 0) {
                    return (
                      <Fragment key={schemeName}>
                        <p className={CSS_CLASSES.SCHEME_NAME}>
                          {schemeName}
                        </p>
                        {items.map((item) => (
                          <Fragment key={item?.id}>
                            {item?.item}
                          </Fragment>
                        ))}
                      </Fragment>
                    );
                  }

                  return <Fragment key={schemeName}></Fragment>;
                },
              );
            }}
          </Form.List>
        )}
      </Form.Item>
    </Fragment>
  );
};

export default ExchangeSection;
