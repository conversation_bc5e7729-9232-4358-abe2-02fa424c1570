import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import { formatNumber } from "@/common/helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import InnerContainer from "@/components/InnerContainer/InnerContainer";
import { Form } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import getColumnsTableReport from "../../ColumnsTableReport";
import FilterReportZone from "../../FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
} from "../../interface";
import { useProjectReportOutletContext } from "../../UseProjectReportOutletContext";
import { useReportOOSStatusesQuery } from "./service";

const ReportOutOfStockStatusPage = () => {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
  } = useProjectReportOutletContext();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

  const reportOOSStatusesQuery = useReportOOSStatusesQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );

  const loading = useMemo(
    () => reportOOSStatusesQuery.isLoading || reportOOSStatusesQuery.isFetching,
    [reportOOSStatusesQuery.isFetching, reportOOSStatusesQuery.isLoading],
  );

  const setFilterForQuery = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);

      if (_.isEqual(filter, values)) {
        reportOOSStatusesQuery.refetch();
      }

      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportOOSStatusesQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportOOSStatusesQuery.data?.count]);

  useEffect(() => {
    setFilterForQuery(advancedFilterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <InnerContainer>
        <FilterReportZone
          form={filterForm}
          loading={loading}
          fields={["keyword", "roleId", "attendance"]}
          onFinish={onFilterFormFinish}
          hideExport
        />

        <CustomTable
          dataSource={reportOOSStatusesQuery.data?.entities.map((entity) => ({
            projectOutlet:
              entity.projectRecordFeature.projectRecord.projectOutlet,
            projectBooth:
              entity.projectRecordFeature.projectRecord.projectBooth,
            id: entity.id,
            attendanceIn:
              entity.projectRecordFeature.attendance?.in ?? undefined,
            attendanceOut:
              entity.projectRecordFeature.attendance?.out ?? undefined,
            projectRecordEmployee:
              entity.projectRecordFeature.projectRecordEmployee,
            projectAgency:
              entity.projectRecordFeature.projectRecord.projectAgency,
            leader: entity.projectRecordFeature.projectRecord.leader,
            values: entity.recordOosStatusValues,
            featureOosZone: entity.featureOosZone,
          }))}
          pagination={pagination}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          className="mt-3"
          rowKey={"id"}
          columns={[
            ...getColumnsTableReport([
              { tableColumn: "outletCode" },
              { tableColumn: "outletName" },
              { tableColumn: "boothName" },
              { tableColumn: "attendance" },
              { tableColumn: "address" },
              { tableColumn: "channelName" },
              { tableColumn: "subChannelName" },
              { tableColumn: "agencyName" },
              { tableColumn: "recordEmployee" },
              { tableColumn: "teamLeader" },
            ]),
            {
              title: "Vị trí",

              dataIndex: "featureOosZone",
              className: "min-w-[120px]",
              render: (featureOosZone) => featureOosZone.name,
            },
            {
              title: componentFeatureQuery.data?.name,
              className: "min-w-[200px]",

              render: (_value: unknown, { values }) => (
                <ul>
                  {values.map((value) => {
                    return (
                      <li key={value.id} className={"pb-3"}>
                        <div className="flex justify-between">
                          <div className="pr-3">
                            {
                              value.featureOosProduct.projectProduct.product
                                .name
                            }
                          </div>
                          <div className="text-blue font-semibold">
                            {value.value || value.value === 0
                              ? formatNumber(value.value)
                              : "_"}
                          </div>
                        </div>
                      </li>
                    );
                  })}
                </ul>
              ),
            },
          ]}
        />
      </InnerContainer>
    </div>
  );
};

export default ReportOutOfStockStatusPage;
