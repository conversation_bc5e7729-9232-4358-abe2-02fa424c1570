import { CURD } from "@/common/constant.ts";
import { useUploadImageMutation } from "@/common/upload-image.helper.ts";
import { useApp } from "@/UseApp.tsx";
import { OrderProductInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { useCallback, useState } from "react";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "../constants.ts";
import { UseFormSubmissionReturn } from "../types.ts";
import {
  createSubmissionData,
  processCustomersData,
  processExchangesData,
  processPhotosData,
  processPurchasesData,
} from "../utils/dataProcessing.ts";
import { validateFormData } from "../utils/validation.ts";

/**
 * Custom hook for handling form submission logic
 */
export const useFormSubmission = (
  form: any,
  action: CURD | undefined,
  featureCustomers: any[],
  featurePhotos: any[],
  photos: any[],
  selectedOrderProducts: OrderProductInterface[],
  selectedExchangeItems: any[],
  createCustomerInformationMutation: any,
  updateCustomerInformationMutation: any,
  editOrderQuery: any,
  onClose: () => void
): UseFormSubmissionReturn => {
  const { showNotification } = useApp();
  const [loading, setLoading] = useState(false);
  const uploadImageMutation = useUploadImageMutation();

  const onSubmit = useCallback(async () => {
    setLoading(true);

    try {
      // Validate and get form data
      const data = await form.validateFields();
      const { customers, time, purchases, exchanges, photoFiles } = data;

      // Validate form data
      validateFormData(data, featurePhotos, photos);

      // Process data for submission
      const customersData = processCustomersData(customers, featureCustomers);
      const purchasesData = processPurchasesData(purchases, selectedOrderProducts);
      const exchangesData = processExchangesData(exchanges, selectedExchangeItems);
      const photosData = await processPhotosData(
        photoFiles,
        photos,
        time,
        uploadImageMutation
      );

      // Create submission data based on action
      const submissionData = createSubmissionData(
        customersData,
        purchasesData,
        exchangesData,
        photosData,
        time,
        action === CURD.CREATE
      );

      // Submit data based on action
      switch (action) {
        case CURD.CREATE:
          await createCustomerInformationMutation.mutateAsync(submissionData);
          showNotification({
            type: "success",
            message: SUCCESS_MESSAGES.CREATE_ORDER,
          });
          break;

        case CURD.UPDATE:
          await updateCustomerInformationMutation.mutateAsync({
            id: editOrderQuery.data?.id ?? 0,
            data: submissionData,
          });
          showNotification({
            type: "success",
            message: SUCCESS_MESSAGES.UPDATE_ORDER,
          });
          break;
      }

      onClose();

    } catch (error: any) {
      showNotification({
        type: "error",
        message: (error.message as unknown as string) ?? ERROR_MESSAGES.GENERAL_ERROR,
      });
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [
    form,
    action,
    featureCustomers,
    featurePhotos,
    photos,
    selectedOrderProducts,
    selectedExchangeItems,
    uploadImageMutation,
    createCustomerInformationMutation,
    updateCustomerInformationMutation,
    editOrderQuery.data?.id,
    onClose,
    showNotification,
  ]);

  return {
    onSubmit,
    loading,
  };
};
