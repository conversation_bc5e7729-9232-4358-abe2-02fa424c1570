/**
 * Constants for EditCustomerInformationCapturingModal
 */

// Modal configuration
export const MODAL_CONFIG = {
  WIDTH: 708,
  MAX_HEIGHT: 650,
  PADDING: 0,
} as const;

// Form section IDs for navigation
export const FORM_SECTION_IDS = {
  CUSTOMERS: "customers",
  PURCHASE: "purchase", 
  GIFT: "gift",
  PHOTO_FILES: "photoFiles",
  TIME: "time",
} as const;

// Form field names
export const FORM_FIELDS = {
  CUSTOMERS: "customers",
  PURCHASES: "purchases", 
  EXCHANGES: "exchanges",
  TIME: "time",
  OTP_CODE: "otpCode",
  ORDER_PRODUCT: "orderProduct",
  PHOTO_FILES: "photoFiles",
} as const;

// CSS classes
export const CSS_CLASSES = {
  SECTION_TITLE: "text-primary font-semibold text-lg",
  SECTION_TITLE_WITH_MARGIN: "text-primary font-semibold text-lg mt-7",
  PHOTO_SECTION_TITLE: "text-primary font-semibold text-lg pb-0 mb-0 mt-[40px]",
  TIME_SECTION_TITLE: "text-primary font-semibold text-lg pb-0 mb-0 mt-[40px]",
  PHOTO_HINT: "font-normal text-xs text-hint mt-0 pt-0 mb-5",
  SCHEME_NAME: "text-blue font-semibold",
  PRODUCT_ITEM: "mt-0 mb-0",
  PRODUCT_CONTAINER: "flex items-center mb-4 w-full",
  INPUT_FULL_WIDTH: "w-full",
  TIME_PICKER_WIDTH: "w-[50%]",
  SCROLL_CONTAINER: "overflow-y-scroll overflow-x-hidden max-h-[650px] pr-10",
  FOOTER: "flex justify-end gap-4 py-4 rounded-b max-md:max-w-full max-md:flex-wrap pl-10 bg-[#F7F8FA] pr-10 pb-4 border-t-[1.5px] border-[#DDE1EA] border-solid border-l-0 border-r-0 border-b-0",
  HEADER: "flex justify-between gap-5 max-md:max-w-full max-md:flex-wrap mr-10",
  HEADER_TITLE: "text-neutral-700 text-2xl font-semibold",
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  CREATE_ORDER: "Tạo đơn hàng thành công.",
  UPDATE_ORDER: "Cập nhật đơn hàng thành công.",
} as const;

// Error messages
export const ERROR_MESSAGES = {
  GENERAL_ERROR: "Có lỗi xảy ra",
  FEATURE_PHOTO_NOT_FOUND: (featurePhotoId: string) => `Feature photo ${featurePhotoId} not found`,
  PHOTO_QUANTITY_ERROR: (name: string, minimum: number, maximum: number) => 
    `${name} phải có số lượng giữa ${minimum} và ${maximum}`,
} as const;

// UI text constants
export const UI_TEXT = {
  MODAL_TITLE_CREATE: "Thêm mới đơn hàng",
  MODAL_TITLE_UPDATE: "Chỉnh sửa đơn hàng",
  CUSTOMER_INFO_TITLE: "Thông tin khách hàng",
  VERIFICATION_TITLE: "Xác thực",
  PURCHASED_PRODUCTS_TITLE: "Sản phẩm đã mua",
  GIFTS_RECEIVED_TITLE: "Quà đã nhận",
  IMAGES_TITLE: "Hình ảnh",
  RECORD_TIME_TITLE: "Thời gian ghi đơn",
  OTP_LABEL: "OTP",
  RECORD_TIME_LABEL: "Thời gian ghi đơn (giờ 24)",
  PRODUCT_SELECT_PLACEHOLDER: "Chọn sản phẩm cần thêm vào đơn hàng",
  PHOTO_HINT_TEXT: "Vui lòng sử dụng ảnh có định dạng .png, .jpg, .jpeg và có dung lượng <= 5mb",
  CLOSE_BUTTON: "Đóng",
  CREATE_BUTTON: "Thêm mới",
  UPDATE_BUTTON: "Cập nhật",
} as const;

// Time picker configuration
export const TIME_PICKER_CONFIG = {
  FORMAT: "HH:mm",
  SHOW_NOW: false,
  MINUTE_OFFSET_START: 1, // +1 minute from start time
  MINUTE_OFFSET_END: 1,   // -1 minute from end time
} as const;

// File upload configuration
export const FILE_UPLOAD_CONFIG = {
  MAX_SIZE_MB: 5,
  ALLOWED_FORMATS: ['.png', '.jpg', '.jpeg'],
} as const;

// Random time addition configuration
export const TIME_CONFIG = {
  MAX_RANDOM_SECONDS: 59,
} as const;

// Form validation rules
export const VALIDATION_RULES = {
  REQUIRED: { required: true },
} as const;
