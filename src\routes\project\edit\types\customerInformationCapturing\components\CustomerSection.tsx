import { CURD } from "@/common/constant.ts";
import { Form, Input } from "antd";
import { Fragment } from "react";
import CustomerInput from "../CustomerInput.tsx";
import { CSS_CLASSES, FORM_SECTION_IDS, UI_TEXT } from "../constants.ts";
import { CustomerSectionProps } from "../types.ts";

/**
 * Customer information section component
 */
const CustomerSection = ({
  featureOrder,
  featureCustomers,
  action,
  isIdentity,
}: CustomerSectionProps) => {
  if (!featureOrder?.hasCustomer) {
    return null;
  }

  return (
    <Fragment>
      {/* Customer Information */}
      <p
        className={CSS_CLASSES.SECTION_TITLE}
        id={FORM_SECTION_IDS.CUSTOMERS}
      >
        {UI_TEXT.CUSTOMER_INFO_TITLE}
      </p>

      <Form.List name={"customers"}>
        {() => {
          return featureCustomers.map((featureCustomer) => (
            <CustomerInput
              key={featureCustomer.id}
              featureCustomer={featureCustomer}
            />
          ));
        }}
      </Form.List>

      {/* OTP Verification Section */}
      {action === CURD.UPDATE && isIdentity && (
        <Fragment>
          <p
            className={CSS_CLASSES.SECTION_TITLE}
            id={FORM_SECTION_IDS.CUSTOMERS}
          >
            {UI_TEXT.VERIFICATION_TITLE}
          </p>

          <Form.Item label={UI_TEXT.OTP_LABEL} required name={"otpCode"}>
            <Input disabled />
          </Form.Item>
        </Fragment>
      )}
    </Fragment>
  );
};

export default CustomerSection;
