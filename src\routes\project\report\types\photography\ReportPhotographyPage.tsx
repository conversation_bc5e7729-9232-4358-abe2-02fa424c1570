import {
  CHUNK_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { createFileAndDownLoad } from "@/common/export-excel.helper.ts";
import { removeVietnameseTones } from "@/common/helper.ts";
import { getImageVariants } from "@/common/image.helper";
import CustomTable from "@/components/CustomTable/CustomTable.tsx";
import ImagesGrid from "@/components/ImagesGrid";
import InnerContainer from "@/components/InnerContainer/InnerContainer.tsx";
import { Col, Form, Row } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import getColumnsTableReport from "../../ColumnsTableReport";
import FilterReportZone from "../../FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
} from "../../interface";
import { useAdvancedFilterFiledsStore } from "../../state.ts";
import { useProjectReportOutletContext } from "../../UseProjectReportOutletContext.tsx";
import { useGetReportPhotoMutation, useReportPhotoQuery } from "./service";

export default function ReportPhotographyPage() {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
    isHideFilter,
    project,
  } = useProjectReportOutletContext();

  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [exportLoading, setExportLoading] = useState(false);

  const reportPhotoQuery = useReportPhotoQuery(projectId, componentFeatureId, {
    ...filter,
    take: pageSize,
    skip: (currentPage - 1) * pageSize,
  });

  const getReportPhotoMutation = useGetReportPhotoMutation(
    projectId,
    componentFeatureId,
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportPhotoQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportPhotoQuery.data?.count]);

  const setFilterForQuery = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);

      if (_.isEqual(filter, values)) {
        reportPhotoQuery.refetch();
      }

      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  useEffect(() => {
    if (!isHideFilter) {
      setFilterForQuery(advancedFilterValues);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues, isHideFilter]);

  useEffect(() => {
    setAdvancedFilterFileds([
      "Agency phụ trách",
      "Role ghi nhận",
      "Ngày chấm công",
      "Nhân viên ghi nhận",
      "Thông tin khách",
      "Mã/ Tên outlet",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Loại booth",
      "Trưởng nhóm quản lý",
    ]);
  }, [setAdvancedFilterFileds]);

  useEffect(() => {
    filterForm.resetFields();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const onExport = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getReportPhotoMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    try {
      const allData = await fetchAllData();

      const data = allData.flatMap((entity) => {
        const { projectOutlet, projectBooth, projectAgency, leader } =
          entity.projectRecord;
        const { in: attendanceIn, out: attendanceOut } = entity.attendance;
        const { projectRecordEmployee, recordPhotos } = entity;

        const timeIn = dayjs(attendanceIn.deviceTime).add(7, "hour").toDate();
        const timeOut = attendanceOut?.deviceTime
          ? dayjs(attendanceOut?.deviceTime).add(7, "hour").toDate()
          : "";

        const fixData = [
          project?.id,
          project?.name,
          projectOutlet.code,
          projectOutlet.name,
          projectBooth.name,
          timeIn,
          timeOut,
          projectOutlet.province?.name,
          projectOutlet.district?.name,
          projectOutlet.projectAgencyChannel.channel.name,
          projectOutlet.subChannel?.name,
          projectAgency.agency.name,
          projectRecordEmployee.employee.role.name,
          projectRecordEmployee.employee.user.id,
          projectRecordEmployee.employee.user.name,
          leader.id,
          leader.user.name,
        ];

        const mergedData = [];

        for (const recordPhoto of recordPhotos) {
          const { dataTimestamp, featurePhoto, image } = recordPhoto;
          mergedData.push([
            ...fixData,
            dayjs(dataTimestamp).add(7, "hour").toDate(),
            featurePhoto.name,
            getImageVariants(image.variants ?? [], "public"),
          ]);
        }

        return mergedData;
      });

      const headers = [
        "ID dự án",
        "Tên dự án",
        "Mã outlet",
        "Tên outlet",
        "Loại booth",
        "Thời gian chấm công vào",
        "Thời gian chấm công ra",
        "Tỉnh/ TP",
        "Quận/ Huyện",
        "Kênh",
        "Nhóm",
        "Agency phụ trách",
        "Role nhân viên chấm công",
        "ID nhân viên chấm công",
        "Họ tên nhân viên chấm công",
        "ID trưởng nhóm quản lý",
        "Họ tên trưởng nhóm quản lý",
        "Thời gian ghi nhận",
        "Loại hình",
        "URL hình",
      ];

      const fileName = removeVietnameseTones(
        componentFeatureQuery.data?.name ?? "",
      );

      await createFileAndDownLoad({
        data,
        headers,
        fileName,
        hyperlinkColumns: [20],
        dateTimeColumns: [6, 7, 18],
      });
    } catch (e) {
      console.error(e);
    } finally {
      setExportLoading(false);
    }
  }, [
    pagination.total,
    getReportPhotoMutation,
    filter,
    componentFeatureQuery.data?.name,
    project?.id,
    project?.name,
  ]);

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <InnerContainer>
        <FilterReportZone
          form={filterForm}
          loading={
            reportPhotoQuery.isLoading ||
            reportPhotoQuery.isFetching ||
            getReportPhotoMutation.isPending ||
            exportLoading
          }
          fields={["keyword", "roleId", "attendance"]}
          onFinish={onFilterFormFinish}
          onExport={onExport}
        />

        <CustomTable
          dataSource={reportPhotoQuery.data?.entities.map((entity) => ({
            projectOutlet: entity.projectRecord.projectOutlet,
            projectBooth: entity.projectRecord.projectBooth,
            id: entity.id,
            attendanceIn: entity.attendance?.in ?? undefined,
            attendanceOut: entity.attendance?.out ?? undefined,
            projectRecordEmployee: entity.projectRecordEmployee,
            projectAgency: entity.projectRecord.projectAgency,
            leader: entity.projectRecord.leader,
            recordPhotos: entity.recordPhotos,
          }))}
          pagination={pagination}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          className="mt-3"
          rowKey={"id"}
          columns={[
            ...getColumnsTableReport([
              { tableColumn: "outletCode" },
              { tableColumn: "outletName" },
              { tableColumn: "boothName" },
              { tableColumn: "attendance" },
              { tableColumn: "address" },
              { tableColumn: "channelName" },
              { tableColumn: "subChannelName" },
              { tableColumn: "agencyName" },
              { tableColumn: "recordEmployee" },
              { tableColumn: "teamLeader" },
            ]),
            {
              title: componentFeatureQuery.data?.name,
              className: "min-w-[330px]",

              render: (_value, { recordPhotos }) => {
                const groupByFeaturePhotoId = _.groupBy(
                  recordPhotos,
                  (o) => o.featurePhotoId,
                );

                return (
                  <>
                    {Object.values(groupByFeaturePhotoId).map(
                      (groupedPhotos) => (
                        <Row
                          key={groupedPhotos[0].featurePhoto.id}
                          className="pb-2 w-full"
                          gutter={8}
                        >
                          <Col md={10} className="text-wrap">
                            {groupedPhotos[0].featurePhoto.name}
                          </Col>
                          <Col md={14}>
                            <ImagesGrid
                              images={groupedPhotos.map((photo) => ({
                                thumbnail: getImageVariants(
                                  photo.image?.variants ?? [],
                                  "thumbnail",
                                ),
                                preview: getImageVariants(
                                  photo.image?.variants ?? [],
                                  "public",
                                ),
                                alt: "Image 1",
                              }))}
                              maxImagesPerRow={3}
                            />
                          </Col>
                        </Row>
                      ),
                    )}
                  </>
                );
              },
            },
          ]}
        />
      </InnerContainer>
    </div>
  );
}
