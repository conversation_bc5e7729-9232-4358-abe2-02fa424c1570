import {
  CHUNK_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { createFileAndDownLoad } from "@/common/export-excel.helper.ts";
import { removeVietnameseTones } from "@/common/helper.ts";
import { getImageVariants } from "@/common/image.helper";
import ImagesGrid from "@/components/ImagesGrid";
import { Col, Form, Row, Table } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import getColumnsTableReport from "../../ColumnsTableReport";
import FilterReportZone from "../../FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
} from "../../interface";
import { useAdvancedFilterFiledsStore } from "../../state.ts";
import { useProjectReportOutletContext } from "../../UseProjectReportOutletContext.tsx";
import {
  useReportMultimediaMutation,
  useReportMultimediaQuery,
} from "./service";

export default function ReportMultiSubjectMultimediaInformationCapturingPage() {
  const {
    componentFeatureQuery,
    projectId,
    componentFeatureId,
    advancedFilterValues,
    project,
  } = useProjectReportOutletContext();

  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isExport, setIsExport] = useState(false);

  const reportMultimediaQuery = useReportMultimediaQuery(
    projectId,
    componentFeatureId,
    {
      ...filter,
      take: pageSize,
      skip: (currentPage - 1) * pageSize,
    },
  );

  const reportMultimediaMutation = useReportMultimediaMutation(
    projectId,
    componentFeatureId,
  );

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportMultimediaQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportMultimediaQuery.data?.count]);

  const setFilterForQuery = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      if (_.isEqual(filter, values)) {
        reportMultimediaQuery.refetch();
      }
      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  useEffect(() => {
    setFilterForQuery(advancedFilterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  useEffect(() => {
    setAdvancedFilterFileds([
      "Agency phụ trách",
      "Role ghi nhận",
      "Ngày chấm công",
      "Nhân viên ghi nhận",
      "Thông tin khách",
      "Mã/ Tên outlet",
      "Tỉnh/ TP",
      "Quận/ Huyện",
      "Kênh",
      "Nhóm",
      "Loại booth",
      "Trưởng nhóm quản lý",
    ]);
  }, [setAdvancedFilterFileds]);

  useEffect(() => {
    filterForm.resetFields();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [location.pathname]);

  const onExport = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          reportMultimediaMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setIsExport(true);

    try {
      const allData = await fetchAllData();

      const data = allData.flatMap((entity) => {
        const { projectOutlet, leader } = entity.projectRecord;
        const { in: attendanceIn, out: attendanceOut } = entity.attendance;
        const {
          projectRecordEmployee,
          recordMultimediaPhotos,
          recordMultimedias,
          createdAt,
        } = entity;

        const timeIn = dayjs(attendanceIn.deviceTime).add(7, "hour").toDate();
        const timeOut = attendanceOut?.deviceTime
          ? dayjs(attendanceOut?.deviceTime).add(7, "hour").toDate()
          : "";
        const createdAtDate = dayjs(createdAt).add(7, "hour").toDate();

        const fixData = [
          // project?.id,
          // project?.name,
          projectOutlet.code,
          projectOutlet.name,
          // projectBooth.name,
          timeIn,
          timeOut,
          projectOutlet.province?.name,
          projectOutlet.district?.name,
          // projectOutlet.projectAgencyChannel.channel.name,
          projectOutlet.subChannel?.name,
          // projectAgency.agency.name,
          // projectRecordEmployee.employee.role.name,
          projectRecordEmployee.employee.user.id,
          projectRecordEmployee.employee.user.name,
          leader.id,
          leader.user.name,
          createdAtDate,
        ];

        const mergedData = [];

        const multimediaGroup = _.groupBy(
          [...recordMultimediaPhotos, ...recordMultimedias],
          (o) => o.featureMultimediaId,
        );

        for (const [key, multimedia] of Object.entries(multimediaGroup)) {
          const title = multimedia[0].featureMultimedia.title;
          const recordMultimediaNote =
            [...recordMultimedias]
              .reverse()
              .find((item) => item.featureMultimediaId === Number(key))
              ?.value ?? "";

          const photos = recordMultimediaPhotos.filter(
            (item) => item.featureMultimediaId === Number(key),
          );

          if (photos.length > 0) {
            for (const photo of photos) {
              const { image } = photo;
              const variants = getImageVariants(image.variants ?? [], "public");
              mergedData.push([
                ...fixData,
                title,
                recordMultimediaNote,
                variants,
              ]);
            }
          } else {
            mergedData.push([...fixData, title, recordMultimediaNote]);
          }
        }
        if (Object.entries(multimediaGroup).length === 0) {
          mergedData.push([...fixData, ""]);
        }

        return mergedData;
      });

      const headers = [
        // "ID dự án",
        // "Tên dự án",
        "Mã outlet",
        "Tên outlet",
        // "Loại booth",
        "Thời gian chấm công vào",
        "Thời gian chấm công ra",
        "Tỉnh/ TP",
        "Phường", // "Quận/ Huyện",
        // "Kênh",
        "Mức outlet",
        // "Agency phụ trách",
        // "Role nhân viên chấm công",
        "ID nhân viên chấm công",
        "Họ tên nhân viên chấm công",
        "ID trưởng nhóm quản lý",
        "Họ tên trưởng nhóm quản lý",
        "Thời gian ghi nhận",
        componentFeatureQuery.data?.name ?? "",
        "Nội dung",
        "URL hình",
      ];

      const fileName = removeVietnameseTones(
        componentFeatureQuery.data?.name ?? "",
      );

      await createFileAndDownLoad({
        data,
        headers,
        fileName,
        hyperlinkColumns: [21],
        dateTimeColumns: [6, 7, 18],
      });
    } catch (e) {
      console.error(e);
    } finally {
      setIsExport(false);
    }
  }, [
    componentFeatureQuery.data?.name,
    filter,
    pagination.total,
    project?.id,
    project?.name,
    reportMultimediaMutation,
  ]);

  return (
    <div>
      <h2>{componentFeatureQuery.data?.name}</h2>
      <div className="bg-white p-10 rounded">
        <FilterReportZone
          form={filterForm}
          loading={
            reportMultimediaQuery.isLoading ||
            reportMultimediaQuery.isFetching ||
            reportMultimediaMutation.isPending ||
            isExport
          }
          fields={["keyword", "roleId", "attendance"]}
          onFinish={onFilterFormFinish}
          onExport={onExport}
        />

        <Table
          dataSource={reportMultimediaQuery.data?.entities.map((entity) => ({
            projectOutlet: entity.projectRecord.projectOutlet,
            projectBooth: entity.projectRecord.projectBooth,
            projectRecord: entity.projectRecord,
            projectAgency: entity.projectRecord.projectAgency,
            projectRecordEmployee: entity.projectRecordEmployee,
            leader: entity.projectRecord.leader,
            id: entity.id,
            recordMultimedias: entity.recordMultimedias,
            recordMultimediaPhotos: entity.recordMultimediaPhotos,
            attendanceIn: entity.attendance?.in,
            attendanceOut: entity.attendance?.out,
          }))}
          scroll={{
            x: "max-content",
            y: pagination.total ? "80vh" : undefined,
          }}
          pagination={pagination}
          className="mt-3"
          rowKey={"id"}
          columns={[
            ...getColumnsTableReport([
              { tableColumn: "outletCode" },
              { tableColumn: "outletName" },
              // { tableColumn: "boothName" },
              { tableColumn: "attendance" },
              { tableColumn: "address" },
              // { tableColumn: "channelName" },
              { tableColumn: "subChannelName" },
              // { tableColumn: "agencyName" },
              { tableColumn: "recordEmployee" },
              { tableColumn: "teamLeader" },
            ]),
            {
              title: componentFeatureQuery.data?.name,

              className: "w-[360px]",
              render: (_value, record) => {
                const { recordMultimedias, recordMultimediaPhotos } = record;

                const multimediaGroup = _.groupBy(
                  [...recordMultimediaPhotos, ...recordMultimedias],
                  (o) => o.featureMultimediaId,
                );

                return Object.entries(multimediaGroup).map(
                  ([key, multimedia]) => (
                    <Row justify="space-between" key={key}>
                      <Col md={5} className="min-w-10">
                        <p>{multimedia[0].featureMultimedia.title}</p>
                      </Col>
                      <Col md={18}>
                        <p className="break-words break-all hyphens-auto">
                          {(() => {
                            const recordMultimedia = [...recordMultimedias]
                              .reverse()
                              .find(
                                (item) =>
                                  item.featureMultimediaId === Number(key),
                              );
                            return (
                              <>
                                {recordMultimedia ? (
                                  recordMultimedia?.value
                                ) : (
                                  <span className="text-hint">
                                    (Chưa có ghi chú)
                                  </span>
                                )}
                              </>
                            );
                          })()}
                        </p>

                        {(() => {
                          const photos = recordMultimediaPhotos.filter(
                            (item) => item.featureMultimediaId === Number(key),
                          );
                          if (photos.length === 0) {
                            return (
                              <span className="text-hint">
                                (Chưa có hình chụp)
                              </span>
                            );
                          }

                          return (
                            <ImagesGrid
                              images={photos.map((photo) => ({
                                thumbnail: getImageVariants(
                                  photo.image?.variants ?? [],
                                  "thumbnail",
                                ),
                                preview: getImageVariants(
                                  photo.image?.variants ?? [],
                                  "public",
                                ),
                                alt: "Image 1",
                              }))}
                              maxImagesPerRow={4}
                            />
                          );
                        })()}
                      </Col>
                    </Row>
                  ),
                );
              },
            },
          ]}
        />
      </div>
    </div>
  );
}
