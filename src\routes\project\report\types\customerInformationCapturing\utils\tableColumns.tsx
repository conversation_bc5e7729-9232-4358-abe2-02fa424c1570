import _ from "lodash";
import getColumnsTableReport from "../../../ColumnsTableReport";
import ItemProductQuantity from "../../../ItemProductQuantity.tsx";
import { TABLE_COLUMNS_CONFIG } from "../constants";
import CustomerCell from "../CustomerCell.tsx";
import CustomerExchangesCell from "../CustomerExchangesCell.tsx";
import CustomerPhotosCell from "../CustomerPhotosCell.tsx";
import {
  RecordOrderCustomerInterface,
  RecordOrderExchangeInterface,
  RecordOrderPhotoInterface,
  RecordOrderPrizeInterface,
  RecordOrderPurchaseInterface,
  RecordOrderSamplingInterface,
} from "../interface";

/**
 * Get table columns configuration for the report
 */
export const getReportTableColumns = () => {
  return [
    {
      title: "Thông tin khách",
      className: "min-w-[200px] max-w-[250px]",
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      render: (_value: any, record: any) => {
        const {
          orderCustomers,
        }: { orderCustomers: RecordOrderCustomerInterface[] } = record;

        return <CustomerCell orderCustomers={orderCustomers} />;
      },
    },
    ...getColumnsTableReport([...TABLE_COLUMNS_CONFIG]),
    {
      title: "Hình đã chụp",
      className: "w-[290px]",
      dataIndex: "orderPhotos",
      render: (orderPhotos: RecordOrderPhotoInterface[]) => (
        <CustomerPhotosCell orderPhotos={orderPhotos} />
      ),
    },
    {
      title: "Sản phẩm đã mua",
      className: "min-w-[150px]",
      dataIndex: "orderPurchases",
      render: (orderPurchases: RecordOrderPurchaseInterface[]) => {
        return orderPurchases.map((orderPurchase, index) => (
          <ItemProductQuantity
            isFirst={index === 0}
            key={orderPurchase.id}
            quantity={orderPurchase.quantity}
            unitName={
              orderPurchase.featureOrderProduct?.projectProduct
                ?.productPackaging?.unit?.name ?? ""
            }
            name={
              orderPurchase?.featureOrderProduct?.projectProduct?.product?.name
            }
            code={
              orderPurchase?.featureOrderProduct?.projectProduct?.product?.code
            }
          />
        ));
      },
    },
    {
      title: "Quà đã nhận",
      className: "min-w-[150px]",
      dataIndex: "orderExchanges",
      render: (orderExchanges: RecordOrderExchangeInterface[]) => (
        <CustomerExchangesCell orderExchanges={orderExchanges} />
      ),
    },
    {
      title: "Quà lucky draw",
      dataIndex: "recordOrderPrizes",
      className: "min-w-[150px]",
      hidden: true,
      render: (recordOrderPrizes: RecordOrderPrizeInterface[]) => {
        const group = _.groupBy(
          recordOrderPrizes,
          (o) => o.projectLuckyDrawResult.projectLuckyDrawItem.projectItem.id,
        );

        return Object.entries(group).map(([, group], index) => {
          return (
            <ItemProductQuantity
              isFirst={index === 0}
              key={group?.[0].id}
              quantity={group.length}
              unitName={
                group?.[0]?.projectLuckyDrawResult?.projectLuckyDrawItem
                  ?.projectItem?.item?.unit?.name ?? ""
              }
              name={
                group?.[0]?.projectLuckyDrawResult?.projectLuckyDrawItem
                  ?.projectItem?.item?.name ?? ""
              }
              code={
                group?.[0]?.projectLuckyDrawResult?.projectLuckyDrawItem
                  ?.projectItem?.item?.code ?? ""
              }
            />
          );
        });
      },
    },
    {
      title: "Sampling đã nhận",
      className: "min-w-[150px]",
      dataIndex: "orderSamplings",
      render: (orderSamplings: RecordOrderSamplingInterface[]) => {
        if (orderSamplings.length === 0) {
          return "Không nhận sampling";
        }
        return orderSamplings.map((orderSampling, index) => (
          <ItemProductQuantity
            isFirst={index === 0}
            key={orderSampling.id}
            quantity={orderSampling.quantity}
            unitName={orderSampling.featureSampling?.unit?.name ?? ""}
            name={
              orderSampling.featureSampling?.projectProduct?.product?.name ?? ""
            }
            code={
              orderSampling.featureSampling?.projectProduct?.product?.code ?? ""
            }
          />
        ));
      },
    },
  ];
};

/**
 * Transform data for table display
 */
export const transformTableData = (entities: any[]) => {
  return entities.map((item) => ({
    id: item.id,
    projectOutlet: item.projectRecordFeature.projectRecord.projectOutlet,
    projectBooth: item.projectRecordFeature.projectRecord.projectBooth,
    projectRecordEmployee: item.projectRecordFeature.projectRecordEmployee,
    attendanceIn: item.projectRecordFeature.attendance.in ?? undefined,
    attendanceOut: item.projectRecordFeature.attendance.out ?? undefined,
    dataTimestamp: item.dataTimestamp,
    projectAgency: item.projectRecordFeature.projectRecord.projectAgency,
    leader: item.projectRecordFeature.projectRecord.leader,
    orderCustomers: item.recordOrderCustomers,
    orderPhotos: item.recordOrderPhotos,
    orderPurchases: item.recordOrderPurchases,
    orderExchanges: item.recordOrderExchanges,
    orderSamplings: item.recordOrderSamplings,
    recordOrderPrizes: item.recordOrderPrizes,
  }));
};
