import { UploadFile } from "antd/lib/index";
import { ERROR_MESSAGES } from "../constants.ts";

/**
 * Validate photo files against feature photo requirements
 */
export const validatePhotos = (
  photoFiles: Record<string, { fileList: UploadFile[] }> | undefined,
  featurePhotos: any[],
  photos: any[]
): void => {
  for (const [featurePhotoId, file] of Object.entries(photoFiles ?? {})) {
    const featurePhoto = featurePhotos.find(
      (featurePhoto) => featurePhoto.id === Number(featurePhotoId),
    );
    
    if (!featurePhoto) {
      throw new Error(ERROR_MESSAGES.FEATURE_PHOTO_NOT_FOUND(featurePhotoId));
    }

    const alreadyUploadedPhotos = photos.filter(
      (photo) => photo.type === featurePhotoId,
    );

    const length =
      (file?.fileList?.filter((file) => file.status !== "done").length ?? 0) + 
      alreadyUploadedPhotos.length;

    if (length < featurePhoto.minimum || length > featurePhoto.maximum) {
      throw new Error(
        ERROR_MESSAGES.PHOTO_QUANTITY_ERROR(
          featurePhoto.name,
          featurePhoto.minimum,
          featurePhoto.maximum
        )
      );
    }
  }
};

/**
 * Validate form data before submission
 */
export const validateFormData = (
  data: any,
  featurePhotos: any[],
  photos: any[]
): void => {
  const { photoFiles } = data;
  
  // Validate photos
  validatePhotos(photoFiles, featurePhotos, photos);
  
  // Add more validation rules here as needed
  // For example: validate required fields, data formats, etc.
};

/**
 * Check if a value is a valid number
 */
export const isValidNumber = (value: any): boolean => {
  return !isNaN(Number(value)) && Number(value) > 0;
};

/**
 * Check if a string is not empty or whitespace only
 */
export const isValidString = (value: any): boolean => {
  return typeof value === 'string' && value.trim().length > 0;
};

/**
 * Validate customer data
 */
export const validateCustomerData = (
  customers: Record<string, any> | undefined,
  featureCustomers: any[]
): boolean => {
  if (!customers) return false;
  
  // Check if all required customers have values
  for (const featureCustomer of featureCustomers) {
    if (featureCustomer.isRequired) {
      const value = customers[featureCustomer.id.toString()];
      if (!value && value !== 0) {
        return false;
      }
    }
  }
  
  return true;
};

/**
 * Validate purchase quantities
 */
export const validatePurchaseQuantities = (
  purchases: Record<string, any> | undefined
): boolean => {
  if (!purchases) return true;
  
  for (const [, quantity] of Object.entries(purchases)) {
    if (quantity && !isValidNumber(quantity)) {
      return false;
    }
  }
  
  return true;
};

/**
 * Validate exchange quantities
 */
export const validateExchangeQuantities = (
  exchanges: Record<string, any> | undefined
): boolean => {
  if (!exchanges) return true;
  
  for (const [, quantity] of Object.entries(exchanges)) {
    if (quantity && !isValidNumber(quantity)) {
      return false;
    }
  }
  
  return true;
};
