import { RecordOrderCustomerInterface } from "./interface";

type CustomerCellProps = {
  orderCustomers: RecordOrderCustomerInterface[];
};
const CustomerCell = ({ orderCustomers }: CustomerCellProps) => {
  if (orderCustomers.length === 0) {
    return <></>;
  }
  const data = orderCustomers.map((item) => {
    const { recordCustomer } = item;
    const { featureCustomer } = recordCustomer;
    let value = "";
    if (recordCustomer?.value) {
      value = recordCustomer.value;
    } else if (
      featureCustomer?.featureCustomerOptions &&
      featureCustomer.featureCustomerOptions.length > 0
    ) {
      value = recordCustomer?.recordCustomerOptions
        .map(
          ({ featureCustomerOptionId }) =>
            featureCustomer.featureCustomerOptions.find(
              (item) => item.id === featureCustomerOptionId,
            )?.name,
        )
        .join(", ");
    } else
      value = recordCustomer?.recordCustomerOptions
        .map((option) => option?.featureCustomerOption?.name)
        .join(", ");

    return {
      label: recordCustomer.featureCustomer?.name,
      value,
    };
  });

  return data
    .sort((a, b) => a.label?.localeCompare(String(b.label)) ?? -1)
    .map((item, index) => {
      return (
        <p key={index} className="m-0 p-0">
          <span className="text-hint">{item.label}:</span> {item.value}
        </p>
      );
    });
};

export default CustomerCell;
