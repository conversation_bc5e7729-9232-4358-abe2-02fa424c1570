import { OrderProductInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import dayjs, { Dayjs } from "dayjs";
import _ from "lodash";

/**
 * Handle product selection for purchase section
 */
export const handlePurchaseSelection = (
  orderProductId: number,
  featureOrderProducts: OrderProductInterface[],
  setSelectedOrderProducts: React.Dispatch<React.SetStateAction<OrderProductInterface[]>>,
  form: any
): void => {
  const orderProduct = featureOrderProducts.find(
    ({ id }) => id === orderProductId,
  );
  
  if (orderProduct) {
    setSelectedOrderProducts((prevState) =>
      _.uniqBy([...prevState, orderProduct], (o) => o.id),
    );
  }
  
  form.resetFields(["orderProduct"]);
};

/**
 * Handle product deletion from purchase section
 */
export const handleProductDeletion = (
  id: number,
  setSelectedOrderProducts: React.Dispatch<React.SetStateAction<OrderProductInterface[]>>
): void => {
  setSelectedOrderProducts((prevState) =>
    prevState.filter((orderProduct) => orderProduct.id !== id),
  );
};

/**
 * Generate disabled time configuration for time picker
 */
export const generateDisabledTime = (attendance: any) => {
  return (date: Dayjs) => {
    const hours = date.get("hours");

    const startArrayHours: number[] = [];
    const startTime = dayjs(attendance?.createdAt);
    startArrayHours.length = startTime.get("hours");

    const endTime = dayjs(attendance?.updatedAt);
    const endArrayHours: number[] = [];
    endArrayHours.length =
      24 - (endTime.get("hours") === 0 ? 23 : endTime.get("hours"));

    return {
      disabledHours: () => [
        ...startArrayHours.fill(0).map((_, index) => index),
        ...endArrayHours.fill(0).map((_, index) => 24 - index),
      ],
      disabledMinutes: () => {
        const disabledMinutes: number[] = [];

        if (hours === startTime.get("hours")) {
          const startArrayMinutes: number[] = [];
          startArrayMinutes.length = startTime.get("minutes") + 1; // +1 start
          disabledMinutes.push(
            ...startArrayMinutes.fill(0).map((_, index) => index),
          );
        }

        if (hours === endTime.get("hours")) {
          const endArrayMinutes: number[] = [];
          endArrayMinutes.length = 60 - endTime.get("minutes") + 1; // -1 end
          disabledMinutes.push(
            ...endArrayMinutes.fill(0).map((_, index) => 60 - index),
          );
        }

        return disabledMinutes;
      },
    };
  };
};

/**
 * Reset form and photos state
 */
export const resetFormState = (
  form: any,
  setPhotos: (photos: any[]) => void,
  cancelCb: () => void
): void => {
  cancelCb();
  setPhotos([]);
  form.resetFields();
};

/**
 * Get product display label for select options
 */
export const getProductDisplayLabel = (orderProduct: OrderProductInterface): string => {
  const { projectProduct } = orderProduct;
  const unitName = projectProduct.productPackaging?.unit.name;
  const { code, name } = projectProduct.product;
  
  return `${unitName} - ${code} - ${name}`;
};

/**
 * Filter available products (not already selected)
 */
export const getAvailableProducts = (
  featureOrderProducts: OrderProductInterface[],
  selectedOrderProducts: OrderProductInterface[]
): OrderProductInterface[] => {
  const selectedIds = selectedOrderProducts.map((item) => item.id);
  return featureOrderProducts.filter(
    (orderProduct) => !selectedIds.includes(orderProduct.id)
  );
};

/**
 * Get initial form values
 */
export const getInitialFormValues = (attendance: any) => {
  return {
    time: dayjs(attendance?.createdAt).add(1, "minute"),
  };
};

/**
 * Check if identity verification is required
 */
export const checkIdentityRequired = (featureCustomers: any[]): boolean => {
  return featureCustomers?.find((item) => item.isIdentity)?.isIdentity ?? false;
};
