import { OrderProductInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { useCallback, useEffect, useMemo } from "react";
import { UseOrderDataReturn } from "../types.ts";
import { processOrderData } from "../utils/dataProcessing.ts";
import { checkIdentityRequired } from "../utils/formHelpers.ts";

/**
 * Custom hook for managing order data and related computed values
 */
export const useOrderData = (
  componentFeature: any,
  editOrderQuery: any,
  form: any,
  setPhotos: (photos: any[]) => void,
  setSelectedOrderProducts: (products: OrderProductInterface[]) => void,
): UseOrderDataReturn => {
  // Memoized feature data
  const featureCustomers = useMemo(
    () => componentFeature?.featureCustomers ?? [],
    [componentFeature?.featureCustomers],
  );

  const featureOrder = useMemo(
    () => componentFeature?.featureOrder,
    [componentFeature?.featureOrder],
  );

  const featureOrderProducts = useMemo(
    () => featureOrder?.featureOrderProducts ?? [],
    [featureOrder?.featureOrderProducts],
  );

  const featureSchemes = useMemo(
    () => componentFeature?.featureSchemes ?? [],
    [componentFeature?.featureSchemes],
  );

  const featurePhotos = useMemo(
    () => componentFeature?.featurePhotos ?? [],
    [componentFeature?.featurePhotos],
  );

  const isIdentity = useMemo(() => {
    return checkIdentityRequired(featureCustomers);
  }, [featureCustomers]);

  // Handle order data processing
  const handleOrderData = useCallback(() => {
    if (!editOrderQuery.data) return;

    processOrderData(
      editOrderQuery.data,
      featureOrderProducts,
      setSelectedOrderProducts,
      setPhotos,
      form,
    );
  }, [
    editOrderQuery.data,
    featureOrderProducts,
    form,
    setPhotos,
    setSelectedOrderProducts,
  ]);

  // Effect to handle order data when it changes
  useEffect(() => {
    if (!editOrderQuery.data) {
      return;
    }

    handleOrderData();
  }, [editOrderQuery.data, handleOrderData]);

  return {
    handleOrderData,
    featureCustomers,
    featureOrder,
    featureOrderProducts,
    featureSchemes,
    featurePhotos,
    isIdentity,
  };
};
