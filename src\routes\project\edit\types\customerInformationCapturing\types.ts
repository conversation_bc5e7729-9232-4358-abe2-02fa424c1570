import { CURD } from "@/common/constant.ts";
import { RecordAttendanceInterface } from "@/routes/project/report/types/attendanceClocking/interface.ts";
import {
  ExchangeProceedInterface,
  OrderProductInterface,
} from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { ComponentFeatureInterface } from "@project/component/feature/interface.ts";
import { UploadFile } from "antd/lib/index";
import { Dayjs } from "dayjs";

// Main modal props interface
export interface EditCustomerInformationCapturingModalProps {
  action: CURD | undefined;
  isOpen: boolean;
  componentFeatureId: number;
  projectId: number;
  attendanceId: number;
  cancelCb: () => void;
  componentFeature?: ComponentFeatureInterface;
  orderId?: number;
  attendance?: RecordAttendanceInterface;
}

// Form data types
export interface FormData {
  customers?: Record<string, RecordType>;
  time?: Dayjs;
  purchases?: Record<string, RecordType>;
  exchanges?: Record<string, string | number | null>;
  photoFiles?: Record<string, { fileList: UploadFile[] }>;
  otpCode?: string;
}

// Exchange item interface for rendering
export interface ExchangeItem {
  item: React.ReactNode;
  schemeName: string;
  id: number;
}

// Photo data for submission
export interface PhotoData {
  featurePhotoId: number;
  imageId: number;
  dataUuid: string;
  dataTimestamp: string;
}

// Customer data for submission
export interface CustomerData {
  featureCustomerId: number;
  featureCustomerOptionIds?: number[];
  value?: string | null;
}

// Purchase data for submission
export interface PurchaseData {
  featureOrderProductId: number;
  quantity: number;
}

// Exchange data for submission
export interface ExchangeData {
  featureSchemeExchangeId: number;
  quantity: number;
}

// Form submission data structure
export interface SubmissionData {
  customers: CustomerData[];
  dataTimestamp: string;
  exchanges: ExchangeData[];
  purchases: PurchaseData[];
  photos: PhotoData[];
  dataUuid?: string;
}

// Update submission data structure
export interface UpdateSubmissionData {
  id: number;
  data: Omit<SubmissionData, 'dataUuid'>;
}

// Hook return types
export interface UseOrderDataReturn {
  handleOrderData: () => void;
  featureCustomers: any[];
  featureOrder: any;
  featureOrderProducts: OrderProductInterface[];
  featureSchemes: any[];
  featurePhotos: any[];
  isIdentity: boolean;
}

export interface UseFormSubmissionReturn {
  onSubmit: () => Promise<void>;
  loading: boolean;
}

export interface UseExchangeItemsReturn {
  selectedExchangeItems: (ExchangeItem | undefined)[];
  renderedExchangeProceeds: (exchangeProceeds: ExchangeProceedInterface[]) => React.ReactNode[];
}

// Component props for form sections
export interface CustomerSectionProps {
  featureOrder: any;
  featureCustomers: any[];
  action: CURD | undefined;
  isIdentity: boolean;
}

export interface PurchaseSectionProps {
  featureOrder: any;
  selectedOrderProducts: OrderProductInterface[];
  featureOrderProducts: OrderProductInterface[];
  onPurchaseSelected: (orderProductId: number) => void;
  deleteOrderProduct: (id: number) => void;
}

export interface ExchangeSectionProps {
  featureOrder: any;
  selectedExchangeItems: (ExchangeItem | undefined)[];
}

export interface PhotoSectionProps {
  featureOrder: any;
  featurePhotos: any[];
  editOrderQuery: any;
}

export interface TimeSectionProps {
  action: CURD | undefined;
  attendance?: RecordAttendanceInterface;
}

// Re-export RecordType from interface.ts for convenience
export type { RecordType } from "./interface.ts";
