import { CURD } from "@/common/constant.ts";
import { Form, TimePicker } from "antd";
import { Fragment } from "react";
import { CSS_CLASSES, FORM_SECTION_IDS, TIME_PICKER_CONFIG, UI_TEXT, VALIDATION_RULES } from "../constants.ts";
import { TimeSectionProps } from "../types.ts";
import { generateDisabledTime } from "../utils/formHelpers.ts";

/**
 * Time selection section component
 */
const TimeSection = ({
  action,
  attendance,
}: TimeSectionProps) => {
  // Only show time section for CREATE action
  if (action === CURD.UPDATE) {
    return null;
  }

  return (
    <div hidden={action === CURD.UPDATE}>
      <p
        className={CSS_CLASSES.TIME_SECTION_TITLE}
        id={FORM_SECTION_IDS.TIME}
      >
        {UI_TEXT.RECORD_TIME_TITLE}
      </p>

      <Form.Item
        label={UI_TEXT.RECORD_TIME_LABEL}
        name={"time"}
        rules={[VALIDATION_RULES.REQUIRED]}
      >
        <TimePicker
          format={TIME_PICKER_CONFIG.FORMAT}
          className={CSS_CLASSES.TIME_PICKER_WIDTH}
          showNow={TIME_PICKER_CONFIG.SHOW_NOW}
          disabledTime={attendance ? generateDisabledTime(attendance) : undefined}
        />
      </Form.Item>
    </div>
  );
};

export default TimeSection;
