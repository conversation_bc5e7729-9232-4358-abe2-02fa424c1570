import {
  CHUNK_SIZE,
  DEFAULT_CURRENT_PAGE,
  DEFAULT_PAGE_SIZE,
} from "@/common/constant";
import { createFileAndDownLoad } from "@/common/export-excel.helper";
import { getImageVariants } from "@/common/image.helper";
import CustomTable from "@/components/CustomTable/CustomTable";
import { ProjectBoothInterface } from "@/routes/project/configOutlet/interface";
import { ProjectEmployeeUserInterface } from "@/routes/project/employee/interface";
import { ProjectAgencyInterface } from "@/routes/project/interface";
import { ProjectOutletInterface } from "@/routes/project/outlet/interface";
import getColumnsTableReport from "@project/report/ColumnsTableReport";
import FilterReportZone from "@project/report/FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
  ProjectRecordEmployeeInterface,
} from "@project/report/interface";
import { useAdvancedFilterFiledsStore } from "@project/report/state.ts";
import { useProjectReportOutletContext } from "@project/report/UseProjectReportOutletContext.tsx";
import { Form } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import AttendanceCell from "../AttendanceCell";
import { AttendanceInterface } from "../interface";
import {
  useGetReportAttendancesMutation,
  useReportAttendanceQuery,
} from "../service";

const timeDiffInHM = (timeDiff: number) => {
  const hours = Math.floor(timeDiff / 3600);
  const minutes = Math.floor((timeDiff - hours * 3600) / 60);
  return `${hours}:${minutes > 10 ? minutes : "0" + minutes} `;
};

export default function EmployeeTab(props: {
  readonly projectId: number;
  readonly componentFeatureId: number;
  readonly advancedFilterValues: AdvancedFilterFormValueInterface;
  readonly isActive: boolean;
}) {
  const { projectId, componentFeatureId, advancedFilterValues, isActive } =
    props;

  const { project } = useProjectReportOutletContext();
  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [isExport, setIsExport] = useState(false);

  const reportAttendanceClockingQuery = useReportAttendanceQuery(
    projectId,
    componentFeatureId,
    { ...filter, take: pageSize, skip: (currentPage - 1) * pageSize },
  );

  const getReportAttendancesMutation = useGetReportAttendancesMutation(
    projectId,
    componentFeatureId,
  );

  const setFilterForQuery = useCallback(
    async (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      if (_.isEqual(filter, values)) {
        await reportAttendanceClockingQuery.refetch();
      }
      setFilter(values);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  useEffect(() => {
    if (isActive) {
      setAdvancedFilterFileds([
        "Agency phụ trách",
        "Role ghi nhận",
        "Ngày chấm công",
        "Nhân viên ghi nhận",
        "Mã/ Tên outlet",
        "Tỉnh/ TP",
        "Quận/ Huyện",
        "Kênh",
        "Nhóm",
        "Loại booth",
        "Trưởng nhóm quản lý",
      ]);
    }
  }, [setAdvancedFilterFileds, isActive]);

  useEffect(() => {
    if (!isActive) {
      return;
    }
    setFilterForQuery(advancedFilterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportAttendanceClockingQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportAttendanceClockingQuery.data?.count]);

  const onExport = useCallback(async () => {
    const total = pagination.total!;

    if (total === 0) {
      return;
    }

    const fetchAllData = async () => {
      const requests = [];

      for (let skip = 0; skip < total; skip += CHUNK_SIZE) {
        requests.push(
          getReportAttendancesMutation.mutateAsync({
            take: CHUNK_SIZE,
            skip: skip,
            ...filter,
          }),
        );
      }

      const results = await Promise.all(requests);
      const allEntities = results.flatMap((result) => result.entities);

      return allEntities;
    };

    setIsExport(true);

    try {
      const entities = await fetchAllData();
      const data =
        entities.map((item) => {
          let attendanceStatus;
          if (item.auto) {
            attendanceStatus = "Hệ thống tự chấm công ra";
          } else if (item?.out?.deviceTime) {
            attendanceStatus = "Hợp lệ";
          } else {
            attendanceStatus = "Chưa chấm công ra";
          }

          return [
            // item.projectRecordEmployee.projectRecord.projectId,
            // project?.name,
            item.projectRecordEmployee.projectRecord.projectOutlet.code,
            item.projectRecordEmployee.projectRecord.projectOutlet.name,
            // item.projectRecordEmployee.projectRecord.projectBooth.name,
            item.projectRecordEmployee.projectRecord.projectOutlet.province
              .name,
            item.projectRecordEmployee.projectRecord.projectOutlet.district
              .name,
            // item.projectRecordEmployee.projectRecord.projectOutlet
            //   .projectAgencyChannel.channel.name,
            item.projectRecordEmployee.projectRecord.projectOutlet.subChannel
              ?.name,
            // item.projectRecordEmployee.projectRecord.projectAgency.agency.name,
            // item.projectRecordEmployee.employee.role.name,
            item.projectRecordEmployee.employee.id,
            item.projectRecordEmployee.employee.user.name,
            item.projectRecordEmployee.projectRecord.leader.id,
            item.projectRecordEmployee.projectRecord.leader.user.name,
            dayjs(item.in.deviceTime).add(7, "hour").toDate(),
            item.out?.deviceTime
              ? dayjs(item.out?.deviceTime).add(7, "hour").toDate()
              : "",
            attendanceStatus,
            item?.out?.deviceTime
              ? timeDiffInHM(
                  dayjs(item.out.deviceTime).unix() -
                    dayjs(item.in.deviceTime).unix(),
                )
              : "",
            getImageVariants(item.in?.image?.variants ?? [], "public"),
            getImageVariants(item.out?.image?.variants ?? [], "public"),
          ];
        }) ?? [];

      const headers = [
        // "ID dự án",
        // "Tên dự án",
        "Mã outlet",
        "Tên outlet",
        // "Loại booth",
        "Tỉnh/ TP",
        "Phường", // "Quận/ Huyện",
        // "Kênh",
        "Mức outlet", // "Nhóm",
        // "Agency phụ trách",
        // "Role nhân viên chấm công",
        "ID nhân viên chấm công",
        "Họ tên nhân viên chấm công",
        "ID trưởng nhóm quản lý",
        "Họ tên trưởng nhóm quản lý",
        "Thời gian chấm công vào",
        "Thời gian chấm công ra",
        "Tình trạng chấm công",
        "Tổng giờ làm",
        "Hình chấm công vào",
        "Hình chấm công ra",
      ];
      const fileName = "Bao cao cham cong ";
      await createFileAndDownLoad({
        data,
        headers,
        fileName,
        hyperlinkColumns: [21, 22],
        dateTimeColumns: [16, 17],
      });
    } catch (e) {
      console.error(e);
    } finally {
      setIsExport(false);
    }
  }, [filter, getReportAttendancesMutation, pagination.total, project?.name]);

  const columns = useMemo(
    () => [
      ...getColumnsTableReport([
        { tableColumn: "outletCode" },
        { tableColumn: "outletName" },
        { tableColumn: "address" },
        { tableColumn: "subChannelName" },
        { tableColumn: "recordEmployee" },
        { tableColumn: "teamLeader" },
      ]),
      {
        title: "Chấm công vào",

        dataIndex: "in",
        className: "min-w-[100px]",
        render: (value: AttendanceInterface) => (
          <AttendanceCell attendance={value} />
        ),
      },
      {
        title: "Chấm công ra",

        dataIndex: "out",
        className: "min-w-[100px]",
        render: (out: AttendanceInterface) => (
          <AttendanceCell attendance={out} />
        ),
      },
      {
        title: "Tổng giờ làm",
        align: "right",

        className: "min-w-[100px]",
        // eslint-disable-next-line
        render: (_: any, record: any) => {
          const { in: inAttendance, out: outAttendance } = record;
          if (!outAttendance) {
            return "";
          }

          return timeDiffInHM(
            dayjs(outAttendance.deviceTime).unix() -
              dayjs(inAttendance.deviceTime).unix(),
          );
        },
      },
    ],
    [],
  );

  return (
    <>
      <FilterReportZone
        form={filterForm}
        onFinish={onFilterFormFinish}
        loading={
          reportAttendanceClockingQuery.isFetching ||
          reportAttendanceClockingQuery.isLoading ||
          isExport
        }
        fields={["keyword", "roleId", "attendance"]}
        onExport={onExport}
      />

      <CustomTable<{
        id: number;
        projectBooth: ProjectBoothInterface;
        projectOutlet: ProjectOutletInterface;
        in: AttendanceInterface;
        out: AttendanceInterface;
        projectRecordEmployee: ProjectRecordEmployeeInterface;
        leader: ProjectEmployeeUserInterface;
        projectAgency: ProjectAgencyInterface;
      }>
        pagination={pagination}
        rowKey={"id"}
        dataSource={reportAttendanceClockingQuery.data?.entities.map(
          (item) => ({
            id: item.id,
            projectBooth: item.projectRecordEmployee.projectRecord.projectBooth,
            projectOutlet:
              item.projectRecordEmployee.projectRecord.projectOutlet,
            in: item.in,
            out: item.out,
            projectRecordEmployee: item.projectRecordEmployee,
            leader: item.projectRecordEmployee.projectRecord.leader,
            projectAgency:
              item.projectRecordEmployee.projectRecord.projectAgency,
          }),
        )}
        scroll={{
          x: "max-content",
          y: pagination.total ? "80vh" : undefined,
        }}
        columns={columns}
      />
    </>
  );
}
