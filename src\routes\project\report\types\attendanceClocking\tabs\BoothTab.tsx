import { DEFAULT_CURRENT_PAGE, DEFAULT_PAGE_SIZE } from "@/common/constant";
import CustomTable from "@/components/CustomTable/CustomTable";
import { ProjectBoothInterface } from "@/routes/project/configOutlet/interface";
import { ProjectEmployeeUserInterface } from "@/routes/project/employee/interface";
import { ProjectAgencyInterface } from "@/routes/project/interface";
import { ProjectOutletInterface } from "@/routes/project/outlet/interface";
import getColumnsTableReport from "@project/report/ColumnsTableReport";
import FilterReportZone from "@project/report/FilterReportZone";
import {
  AdvancedFilterFormValueInterface,
  AdvancedFilterInterface,
  ProjectRecordEmployeeInterface,
} from "@project/report/interface";
import { useAdvancedFilterFiledsStore } from "@project/report/state.ts";
import { useProjectReportOutletContext } from "@project/report/UseProjectReportOutletContext.tsx";
import { Form } from "antd";
import dayjs from "dayjs";
import _ from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useReportAttendanceBoothsQuery } from "../service";

export default function BoothTab(props: {
  readonly projectId: number;
  readonly componentFeatureId: number;
  readonly advancedFilterValues: AdvancedFilterFormValueInterface;
  readonly isActive: boolean;
}) {
  const { projectId, componentFeatureId, advancedFilterValues, isActive } =
    props;

  const { roles } = useProjectReportOutletContext();

  const [filterForm] = Form.useForm();
  const [filter, setFilter] = useState<AdvancedFilterInterface>({
    attendanceStartDate: dayjs().startOf("date").toDate(),
    attendanceEndDate: dayjs().endOf("date").toDate(),
  });
  const [currentPage, setCurrentPage] = useState(DEFAULT_CURRENT_PAGE);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const { setFileds: setAdvancedFilterFileds } = useAdvancedFilterFiledsStore();

  const reportAttendanceBoothsQuery = useReportAttendanceBoothsQuery(
    projectId,
    componentFeatureId,
    { ...filter, take: pageSize, skip: (currentPage - 1) * pageSize },
  );

  useEffect(() => {
    if (isActive) {
      setAdvancedFilterFileds([
        "Agency phụ trách",
        "Ngày chấm công",
        "Mã/ Tên outlet",
        "Tỉnh/ TP",
        "Quận/ Huyện",
        "Kênh",
        "Nhóm",
        "Loại booth",
        "Trưởng nhóm quản lý",
      ]);
    }
  }, [setAdvancedFilterFileds, isActive]);

  const pagination = useMemo(() => {
    return {
      current: currentPage,
      total: reportAttendanceBoothsQuery.data?.count,
      pageSize: pageSize,
      onChange: (page: number) => {
        setCurrentPage(page);
      },
      showTotal: (total: number) => `Số kết quả trả về: ${total}`,
      showSizeChanger: true,
      onShowSizeChange: (_current: number, size: number) => {
        setPageSize(size);
        setCurrentPage(DEFAULT_CURRENT_PAGE);
      },
    };
  }, [currentPage, pageSize, reportAttendanceBoothsQuery.data?.count]);

  const setFilterForQuery = useCallback(
    (values: AdvancedFilterFormValueInterface) => {
      setCurrentPage(DEFAULT_CURRENT_PAGE);
      if (_.isEqual(filter, values)) {
        reportAttendanceBoothsQuery.refetch();
      } else {
        setFilter(values);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [filter],
  );

  useEffect(() => {
    if (!isActive) {
      return;
    }
    setFilterForQuery(advancedFilterValues);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [advancedFilterValues]);

  const onFilterFormFinish = useCallback(() => {
    const values = filterForm.getFieldsValue();

    if (values.attendance) {
      const [attendanceStartDate, attendanceEndDate] = values.attendance;
      values.attendanceStartDate = attendanceStartDate
        ? dayjs(attendanceStartDate).startOf("date").toDate()
        : undefined;
      values.attendanceEndDate = attendanceEndDate
        ? dayjs(attendanceEndDate).endOf("date").toDate()
        : undefined;

      delete values.attendance;
    }
    setFilterForQuery(values);
  }, [filterForm, setFilterForQuery]);

  return (
    <>
      <FilterReportZone
        form={filterForm}
        onFinish={onFilterFormFinish}
        loading={
          reportAttendanceBoothsQuery.isLoading ||
          reportAttendanceBoothsQuery.isFetching
        }
        fields={["keyword", "projectBoothId", "attendance"]}
        hideExport={true}
        placeholder={"Tìm theo outlet"}
      />

      <CustomTable<{
        id: number;
        projectOutlet: ProjectOutletInterface;
        projectRecordEmployees: ProjectRecordEmployeeInterface[];
        projectBooth: ProjectBoothInterface;
        leader: ProjectEmployeeUserInterface;
        date: string;
        projectAgency: ProjectAgencyInterface;
      }>
        rowKey={"id"}
        scroll={{
          x: "max-content",
          y: pagination.total ? "80vh" : undefined,
        }}
        dataSource={reportAttendanceBoothsQuery.data?.entities.map((item) => ({
          id: item.id,
          projectOutlet: item.projectOutlet,
          projectRecordEmployees: item.projectRecordEmployees,
          projectBooth: item.projectBooth,
          leader: item.leader,
          date: item.workday,
          projectAgency: item.projectAgency,
        }))}
        pagination={pagination}
        columns={[
          ...getColumnsTableReport([
            { tableColumn: "outletCode" },
            { tableColumn: "outletName" },
            { tableColumn: "address" },
            { tableColumn: "date" },
            { tableColumn: "subChannelName" },
            { tableColumn: "teamLeader" },
          ]),
          {
            title: "Nhân viên đã chấm công vào",

            className: "min-w-[100px]",
            // eslint-disable-next-line
            render: (_, record: any) => {
              const {
                projectRecordEmployees,
              }: { projectRecordEmployees: ProjectRecordEmployeeInterface[] } =
                record;

              return (
                <>
                  {roles?.map((role) => {
                    const projectRecordEmployeesFilterd =
                      projectRecordEmployees?.filter(
                        (projectRecordEmployee) =>
                          projectRecordEmployee.employee.roleId === role.id &&
                          projectRecordEmployee?.recordAttendances?.length > 0,
                      );
                    return (
                      <p className="p-0 m-0" key={role.id}>
                        {projectRecordEmployeesFilterd?.length ? (
                          <span className="font-semibold">
                            {projectRecordEmployeesFilterd?.length}
                          </span>
                        ) : (
                          <span className="text-primary font-semibold">0</span>
                        )}
                        &nbsp;
                        {role.name}
                      </p>
                    );
                  })}
                </>
              );
            },
          },
          {
            title: "Nhân viên đã chấm công ra",

            className: "min-w-[100px]",
            // eslint-disable-next-line
            render: (_, record: any) => {
              const {
                projectRecordEmployees,
              }: { projectRecordEmployees: ProjectRecordEmployeeInterface[] } =
                record;

              return (
                <>
                  {roles?.map((role) => {
                    const projectRecordEmployeesFilterd =
                      projectRecordEmployees?.filter(
                        (projectRecordEmployee) =>
                          projectRecordEmployee.employee.roleId === role.id &&
                          projectRecordEmployee?.recordAttendances.find(
                            (item) => item.auto || item.out,
                          ),
                      );
                    return (
                      <p className="p-0 m-0" key={role.id}>
                        {projectRecordEmployeesFilterd?.length ? (
                          <span className="font-semibold">
                            {projectRecordEmployeesFilterd?.length}
                          </span>
                        ) : (
                          <span className="text-primary font-semibold">0</span>
                        )}
                        &nbsp;
                        {role.name}
                      </p>
                    );
                  })}
                </>
              );
            },
          },
        ]}
      />
    </>
  );
}
