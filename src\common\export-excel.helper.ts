import dayjs from "dayjs";
import Excel, { Column } from "exceljs";
import _ from "lodash";

export const generateUniqueCode = (str: string) => {
  // Hash the string using a simple hashing function (you can use more sophisticated hashing algorithms if needed)
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = (hash << 5) + str.charCodeAt(i);
    hash = hash & hash; // Convert to 32bit integer
    hash = Math.abs(hash); // Ensure positive value
  }
  // Convert the hash to a hexadecimal string
  return hash.toString(16);
};

export const downloadFile = (wb: Excel.Workbook, fileName: string) => {
  wb.xlsx
    .writeBuffer()
    .then((buffer) => {
      const blob = new Blob([buffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;",
      });
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(blob);
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
    })
    .catch((error) => {
      throw error;
    });
};

export const numToCol = (num: number): string => {
  let result = "";

  while (num > 0) {
    let remainder = num % 26;

    if (remainder == 0) {
      remainder = 26;
      num--;
    }

    const letter = String.fromCharCode(remainder + 64);
    result = letter + result;

    num = Math.floor(num / 26);
  }

  return result;
};

export const formatHeader = (
  cell: Excel.Cell,
  options?: { horizontal?: "center" | "left" | "right" },
) => {
  cell.style = {
    font: { size: 14, color: { argb: "ffffff" } },
    border: {
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" },
      top: { style: "thin" },
    },
    fill: {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "ff1868ae" },
    },
    alignment: {
      vertical: "middle",
      horizontal: options?.horizontal ?? undefined,
    },
  };
};

export const formatCellHeader = (
  cell: Excel.Cell,
  options?: {
    horizontal?: "center" | "left" | "right";
  },
) => {
  const { horizontal = "center" } = options || {};
  cell.border = {
    bottom: { style: "thin" },
    left: { style: "thin" },
    right: { style: "thin" },
    top: { style: "thin" },
  };
  cell.alignment = { vertical: "middle", horizontal, wrapText: true };
  cell.font = {
    name: "Arial",
    size: 12,
    bold: true,
  };
};

const formatCell = (cell: Excel.Cell, isDateTime = false) => {
  cell.border = {
    bottom: { style: "thin" },
    left: { style: "thin" },
    right: { style: "thin" },
    top: { style: "thin" },
  };

  if (isDateTime) {
    cell.numFmt = "dd/mm/yyyy hh:mm:ss";
  }
};

type ExcelCellValue = string | undefined | number | Date | null;
export const createExcelFile = async ({
  data,
  headers,
  hyperlinkColumns = [],
  dateTimeColumns = [],
}: {
  data: ExcelCellValue[][];
  headers: string[];
  hyperlinkColumns?: number[];
  dateTimeColumns?: number[];
}) => {
  const workbook = new Excel.Workbook();
  const worksheet = workbook.addWorksheet("data", {});

  worksheet.columns = headers.map((item) => ({
    header: item,
    key: generateUniqueCode(item),
    width: 20,
  }));

  headers.forEach((_, index) => {
    const header = worksheet.getCell(`${numToCol(index + 1)}1`);
    formatHeader(header);

    const cell = worksheet.getCell(`${numToCol(index + 1)}2`);
    const isDateTime = dateTimeColumns.find((col) => col === index + 1);
    formatCell(cell, !!isDateTime);
  });

  const BEGIN_INDEX_OF_FILE = 2;

  if (hyperlinkColumns.length) {
    data.forEach((row, index) => {
      worksheet.insertRow(BEGIN_INDEX_OF_FILE + index + 1, row, "i+");
      hyperlinkColumns.forEach((col) => {
        if (row[col + 1]) {
          worksheet.getCell(
            `${numToCol(col)}${BEGIN_INDEX_OF_FILE + index + 1}`,
          ).value = {
            text: row[col + 1]?.toString() ?? "",
            hyperlink: row[col + 1]?.toString() ?? "",
            tooltip: row[col + 1]?.toString() ?? "",
          };
        }
      });
    });
  } else {
    worksheet.insertRows(BEGIN_INDEX_OF_FILE + 1, data, "i+");
  }
  worksheet.spliceRows(BEGIN_INDEX_OF_FILE, 1);

  return workbook;
};

export const createFileAndDownLoad = async ({
  data,
  headers,
  fileName,
  hyperlinkColumns = [],
  dateTimeColumns = [],
}: {
  data: (string | undefined | number | Date | null)[][];
  headers: string[];
  fileName: string;
  hyperlinkColumns?: number[];
  dateTimeColumns?: number[];
}) => {
  const workbook = await createExcelFile({
    data,
    headers,
    hyperlinkColumns,
    dateTimeColumns,
  });

  downloadFile(
    workbook,
    `${_.upperFirst(fileName.trim().toLocaleLowerCase())} ${dayjs().format("DDMMYY")}.xlsx`,
  );
};

export const createTemplateAndDownload = async (
  columns: Array<Partial<Column>>,
  fileName: string,
  columnsTypeList?: { index: number; list: string[] }[],
  descriptionColumns?: {
    message: string;
    color: string;
    background?: string;
  }[],
) => {
  const workbook = new Excel.Workbook();
  const worksheet = workbook.addWorksheet("Data", {});
  worksheet.columns = columns.map((item) => ({
    header: item.header,
    key: generateUniqueCode(item.header as string),
    width: 20,
  }));

  columns.forEach((_, index) => {
    const header = worksheet.getCell(`${numToCol(index + 1)}1`);
    formatHeader(header);
  });
  if (descriptionColumns?.length) {
    const row = worksheet.addRow(
      descriptionColumns?.map((item) => item.message),
    );
    row.eachCell((cell, index) => {
      const item = descriptionColumns?.[index - 1];
      cell.style = {
        font: { color: { argb: item?.color } },
        fill: {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: item?.background },
        },
        border: {
          bottom: { style: "thin" },
          left: { style: "thin" },
          right: { style: "thin" },
          top: { style: "thin" },
        },
      };
    });
  }

  if (columnsTypeList?.length) {
    columnsTypeList?.forEach((colType) => {
      const { index, list } = colType;
      const beginIndex = descriptionColumns?.length ? 3 : 2;

      for (let i = beginIndex; i < 100; i++) {
        worksheet.getCell(`${numToCol(index)}${i}`).dataValidation = {
          type: "list",
          allowBlank: false,
          formulae: ['"' + list.join(",") + '"'],
          showErrorMessage: true,
          errorStyle: "error",
          error: "The value not in the list",
        };
      }
    });
  }

  downloadFile(workbook, `${fileName}.xlsx`);
};

interface CreateTemplateMultipleSheetAndDownloadInterface {
  columns: Array<Partial<Column>>;
  fileName: string;
  columnsTypeList?: { index: number; list: string[] }[];
  descriptionColumns?: {
    message: string;
    color: string;
    background?: string;
  }[];
  extraWorksheetsData?: {
    name: string;
    headers: string[];
    data: (string | undefined | number | Date | null)[][];
  }[];
  columnsTypeListWithSheet?: { index: number; range: string }[];
}
export const createTemplateMultipleSheetAndDownload = ({
  columns,
  fileName,
  columnsTypeList,
  descriptionColumns,
  extraWorksheetsData,
  columnsTypeListWithSheet,
}: CreateTemplateMultipleSheetAndDownloadInterface) => {
  const workbook = new Excel.Workbook();
  const worksheet = workbook.addWorksheet("Data", {});

  worksheet.columns = columns.map((item) => ({
    header: item.header,
    key: generateUniqueCode(item.header as string),
    width: 20,
  }));

  columns.forEach((_, index) => {
    const header = worksheet.getCell(`${numToCol(index + 1)}1`);
    formatHeader(header);
  });
  if (descriptionColumns?.length) {
    const row = worksheet.addRow(
      descriptionColumns?.map((item) => item.message),
    );
    row.eachCell((cell, index) => {
      const item = descriptionColumns?.[index - 1];
      cell.style = {
        font: { color: { argb: item?.color } },
        fill: {
          type: "pattern",
          pattern: "solid",
          fgColor: { argb: item?.background },
        },
        border: {
          bottom: { style: "thin" },
          left: { style: "thin" },
          right: { style: "thin" },
          top: { style: "thin" },
        },
      };
    });
  }

  if (extraWorksheetsData?.length) {
    extraWorksheetsData?.forEach((item) => {
      const { name, headers, data } = item;
      const worksheet = workbook.addWorksheet(name, {});
      worksheet.columns = headers.map((item) => ({
        header: item,
        key: generateUniqueCode(item),
        width: 20,
      }));

      data.forEach((row) => {
        worksheet.insertRow(2, row, "i+");
      });
    });
  }

  if (columnsTypeList?.length) {
    columnsTypeList?.forEach((colType) => {
      const { index, list } = colType;
      const beginIndex = descriptionColumns?.length ? 3 : 2;

      for (let i = beginIndex; i < 100; i++) {
        worksheet.getCell(`${numToCol(index)}${i}`).dataValidation = {
          type: "list",
          allowBlank: false,
          formulae: ['"' + list.join(",") + '"'],
          showErrorMessage: true,
          errorStyle: "error",
          error: "The value not in the list",
        };
      }
    });
  }

  if (columnsTypeListWithSheet?.length) {
    columnsTypeListWithSheet?.forEach((colType) => {
      const { index, range } = colType;
      const beginIndex = descriptionColumns?.length ? 3 : 2;

      for (let i = beginIndex; i < 100; i++) {
        worksheet.getCell(`${numToCol(index)}${i}`).dataValidation = {
          type: "list",
          allowBlank: false,
          formulae: [range],
          showErrorMessage: true,
          errorStyle: "error",
          error: "The value not in the list",
        };
      }
    });
  }

  downloadFile(workbook, `${fileName}.xlsx`);
};
