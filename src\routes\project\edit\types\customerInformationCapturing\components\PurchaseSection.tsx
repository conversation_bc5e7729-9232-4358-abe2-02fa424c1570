import { filterOption } from "@/common/helper.ts";
import { DeleteOutlined } from "@ant-design/icons";
import { Button, Form, InputNumber, Select } from "antd";
import { Fragment } from "react";
import { CSS_CLASSES, FORM_SECTION_IDS, UI_TEXT } from "../constants.ts";
import { PurchaseSectionProps } from "../types.ts";
import { getAvailableProducts, getProductDisplayLabel } from "../utils/formHelpers.ts";

/**
 * Purchase products section component
 */
const PurchaseSection = ({
  featureOrder,
  selectedOrderProducts,
  featureOrderProducts,
  onPurchaseSelected,
  deleteOrderProduct,
}: PurchaseSectionProps) => {
  if (!featureOrder?.hasPurchase) {
    return null;
  }

  const availableProducts = getAvailableProducts(featureOrderProducts, selectedOrderProducts);

  return (
    <Fragment>
      <p
        className={CSS_CLASSES.SECTION_TITLE_WITH_MARGIN}
        id={FORM_SECTION_IDS.PURCHASE}
      >
        {UI_TEXT.PURCHASED_PRODUCTS_TITLE}
      </p>

      {/* Selected Products List */}
      <Form.List name={"purchases"}>
        {() => {
          return selectedOrderProducts.map((orderProduct) => {
            const { projectProduct, id } = orderProduct;
            const unitName = projectProduct.productPackaging?.unit.name;
            const { code, name } = projectProduct.product;

            return (
              <div
                className={CSS_CLASSES.PRODUCT_CONTAINER}
                key={`selectedOrderProducts${id}`}
              >
                <Form.Item
                  label={`${unitName} - ${code} - ${name}`}
                  name={`${id}`}
                  className={CSS_CLASSES.INPUT_FULL_WIDTH}
                >
                  <InputNumber
                    className={CSS_CLASSES.INPUT_FULL_WIDTH}
                    controls={false}
                  />
                </Form.Item>

                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  className="ml-2"
                  onClick={() => {
                    deleteOrderProduct(id);
                  }}
                />
              </div>
            );
          });
        }}
      </Form.List>

      {/* Product Selection Dropdown */}
      <Form.Item name={"orderProduct"}>
        <Select
          showSearch={true}
          placeholder={UI_TEXT.PRODUCT_SELECT_PLACEHOLDER}
          filterOption={filterOption}
          options={availableProducts.map((orderProduct) => ({
            label: getProductDisplayLabel(orderProduct),
            value: orderProduct.id,
          }))}
          onSelect={onPurchaseSelected}
        />
      </Form.Item>
    </Fragment>
  );
};

export default PurchaseSection;
