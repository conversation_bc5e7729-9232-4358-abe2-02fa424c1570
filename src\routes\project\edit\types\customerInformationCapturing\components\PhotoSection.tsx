import FormImageUploadComponent from "@/components/formImageUploadComponent/FormImageUploadComponent.tsx";
import { Fragment } from "react";
import { CSS_CLASSES, FORM_SECTION_IDS, UI_TEXT } from "../constants.ts";
import { PhotoSectionProps } from "../types.ts";

/**
 * Photo upload section component
 */
const PhotoSection = ({
  featureOrder,
  featurePhotos,
  editOrderQuery,
}: PhotoSectionProps) => {
  if (!featureOrder?.hasPhoto) {
    return null;
  }

  return (
    <Fragment>
      <p
        className={CSS_CLASSES.PHOTO_SECTION_TITLE}
        id={FORM_SECTION_IDS.PHOTO_FILES}
      >
        {UI_TEXT.IMAGES_TITLE}
      </p>
      
      <p className={CSS_CLASSES.PHOTO_HINT}>
        {UI_TEXT.PHOTO_HINT_TEXT}
      </p>

      {featurePhotos.map((featurePhoto, index) => (
        <FormImageUploadComponent
          key={index}
          label={featurePhoto.name}
          fieldName={["photoFiles", `${featurePhoto.id}`]}
          max={featurePhoto.maximum}
          required={featurePhoto.minimum > 0}
          imagesFile={editOrderQuery.data?.recordOrderPhotos
            ?.filter(
              (recordOrderPhoto: any) =>
                recordOrderPhoto.recordPhoto.featurePhotoId ===
                featurePhoto.id,
            )
            .map(
              (recordOrderPhoto: any) =>
                recordOrderPhoto.recordPhoto.image,
            )}
        />
      ))}
    </Fragment>
  );
};

export default PhotoSection;
