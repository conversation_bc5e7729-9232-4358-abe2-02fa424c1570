import { OrderProductInterface } from "@project/component/feature/config/types/customerInformationCapturing/interface.ts";
import { UploadFile } from "antd/lib/index";
import dayjs from "dayjs";
import _ from "lodash";
import { v4 as uuidv4 } from "uuid";
import { RecordType } from "../interface.ts";
import {
  CustomerData,
  ExchangeData,
  FormData,
  PhotoData,
  PurchaseData,
  SubmissionData,
} from "../types.ts";

/**
 * Process order data from API response and set form values
 */
export const processOrderData = (
  editOrderData: any,
  featureOrderProducts: OrderProductInterface[],
  setSelectedOrderProducts: (products: OrderProductInterface[]) => void,
  setPhotos: (photos: any[]) => void,
  form: any
) => {
  const {
    recordOrderCustomers,
    recordOrderPurchases,
    recordOrderExchanges,
    recordOrderPhotos,
    dataTimestamp,
  } = editOrderData ?? {};

  // Process customers data
  const customers: Record<string, RecordType> = {};
  let otpCode: undefined | string = undefined;

  for (const recordOrderCustomer of recordOrderCustomers ?? []) {
    const { featureCustomerId, value, recordCustomerOptions, otpDelivery } =
      recordOrderCustomer.recordCustomer;
    if (otpDelivery) {
      otpCode = otpDelivery.otpCode;
    }

    if (value) {
      customers[`${featureCustomerId}`] = value;
    }
    if (recordCustomerOptions.length > 0) {
      customers[`${featureCustomerId}`] =
        recordCustomerOptions[0].featureCustomerOptionId;
    }
  }

  // Process purchases data
  const selectedOrderProducts: OrderProductInterface[] = [];
  const purchases: Record<string, RecordType> = {};
  for (const recordOrderPurchase of recordOrderPurchases ?? []) {
    const { featureOrderProductId, quantity } = recordOrderPurchase;

    const orderProduct = featureOrderProducts.find(
      ({ id }) => id === featureOrderProductId,
    );
    if (orderProduct) {
      selectedOrderProducts.push(orderProduct);
    }

    purchases[`${featureOrderProductId}`] = quantity;
  }
  setSelectedOrderProducts(selectedOrderProducts);

  // Process exchanges data
  const exchanges: Record<string, string | number | null> = {};
  for (const recordOrderExchange of recordOrderExchanges ?? []) {
    const { featureSchemeExchangeId, quantity } = recordOrderExchange;
    exchanges[`${featureSchemeExchangeId}`] = quantity;
  }

  // Process photos data
  setPhotos(
    recordOrderPhotos?.map((recordOrderPhoto: any) => ({
      type: recordOrderPhoto.recordPhoto.featurePhotoId.toString(),
      image: recordOrderPhoto.recordPhoto.image,
      dataUuid: recordOrderPhoto.recordPhoto.dataUuid,
      dataTimestamp: recordOrderPhoto.recordPhoto.dataTimestamp,
    })) ?? [],
  );

  // Set form values
  form.setFieldsValue({
    customers,
    purchases,
    exchanges,
    time: dayjs(dataTimestamp),
    otpCode,
  });
};

/**
 * Process customers data for submission
 */
export const processCustomersData = (
  customers: Record<string, RecordType> | undefined,
  featureCustomers: any[]
): CustomerData[] => {
  const customersData: CustomerData[] = [];
  
  for (const [featureCustomerId, value] of Object.entries(customers ?? {})) {
    if (typeof value === "number") {
      customersData.push({
        featureCustomerId: Number(featureCustomerId),
        featureCustomerOptionIds: [value],
      });
    }
    if (typeof value === "string") {
      customersData.push({
        featureCustomerId: Number(featureCustomerId),
        value: String(value),
      });
    }
  }

  // Ensure all feature customers are included
  if (customersData.length !== featureCustomers.length) {
    for (const featureCustomer of featureCustomers) {
      if (
        !customersData.find(
          (customer) => customer.featureCustomerId === featureCustomer.id,
        )
      ) {
        customersData.push({
          featureCustomerId: featureCustomer.id,
          value: null,
        });
      }
    }
  }

  return customersData;
};

/**
 * Process purchases data for submission
 */
export const processPurchasesData = (
  purchases: Record<string, RecordType> | undefined,
  selectedOrderProducts: OrderProductInterface[]
): PurchaseData[] => {
  const purchasesData: PurchaseData[] = [];
  
  for (const [featureOrderProductId, quantity] of Object.entries(purchases ?? {})) {
    if (Number(quantity)) {
      const selectedOrderProduct = selectedOrderProducts.find(
        (selectedOrderProduct) =>
          selectedOrderProduct.id === Number(featureOrderProductId),
      );

      if (selectedOrderProduct) {
        purchasesData.push({
          featureOrderProductId: Number(featureOrderProductId),
          quantity: Number(quantity),
        });
      }
    }
  }

  return purchasesData;
};

/**
 * Process exchanges data for submission
 */
export const processExchangesData = (
  exchanges: Record<string, string | number | null> | undefined,
  selectedExchangeItems: any[]
): ExchangeData[] => {
  const exchangesData: ExchangeData[] = [];
  
  for (const [featureSchemeExchangeId, quantity] of Object.entries(exchanges ?? {})) {
    if (Number(quantity)) {
      const groupedItem = selectedExchangeItems.find(
        (groupedItem) =>
          groupedItem?.id === Number(featureSchemeExchangeId),
      );
      if (groupedItem) {
        exchangesData.push({
          featureSchemeExchangeId: Number(featureSchemeExchangeId),
          quantity: Number(quantity),
        });
      }
    }
  }

  return exchangesData;
};

/**
 * Process photos data for submission
 */
export const processPhotosData = async (
  photoFiles: Record<string, { fileList: UploadFile[] }> | undefined,
  photos: any[],
  time: dayjs.Dayjs,
  uploadImageMutation: any
): Promise<PhotoData[]> => {
  const photosData: PhotoData[] = [];

  // Process new photo uploads
  for (const [featurePhotoId, photoFile] of Object.entries(photoFiles ?? {})) {
    if (photoFile) {
      const { fileList } = photoFile;
      for (const file of fileList) {
        if (file.originFileObj) {
          const result = await uploadImageMutation.mutateAsync(
            file.originFileObj,
          );
          if (result?.id)
            photosData.push({
              featurePhotoId: Number(featurePhotoId),
              imageId: result.id,
              dataUuid: uuidv4(),
              dataTimestamp: time.toISOString(),
            });
        }
      }
    }
  }

  // Process existing photos
  for (const photo of photos) {
    const { image, dataUuid, dataTimestamp } = photo;

    photosData.push({
      featurePhotoId: Number(photo.type),
      imageId: image.id,
      dataUuid: dataUuid ?? "",
      dataTimestamp: dataTimestamp ?? "",
    });
  }

  return photosData;
};

/**
 * Create submission data object
 */
export const createSubmissionData = (
  customersData: CustomerData[],
  purchasesData: PurchaseData[],
  exchangesData: ExchangeData[],
  photosData: PhotoData[],
  time: dayjs.Dayjs,
  isCreate: boolean = false
): SubmissionData => {
  const baseData = {
    customers: customersData,
    dataTimestamp: isCreate 
      ? dayjs(time).add(_.random(59 - dayjs(time).second()), "second").toISOString()
      : time.toISOString(),
    exchanges: exchangesData,
    purchases: purchasesData,
    photos: photosData,
  };

  if (isCreate) {
    return {
      ...baseData,
      dataUuid: uuidv4(),
    };
  }

  return baseData;
};
